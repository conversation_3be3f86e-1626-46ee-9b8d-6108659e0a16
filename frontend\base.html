<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}TradingView Bot Dashboard{% endblock %}</title>
    <meta name="description" content="Professional trading dashboard for automated TradingView bot">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/static/images/logo.svg">
    
    <!-- CSS Framework -->
    <link rel="stylesheet" href="/static/css/main.css">
    <link rel="stylesheet" href="/static/css/dashboard.css">
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    {% block head %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar" role="navigation">
        <div class="nav-container">
            <!-- Logo -->
            <div class="nav-logo">
                <img src="/static/images/logo.svg" alt="Trading Bot" class="nav-logo-img">
                <span class="nav-title">TradingView Bot</span>
                <span class="nav-badge" id="connection-status">
                    <i class="fas fa-circle"></i>
                    <span class="status-text">Connecting...</span>
                </span>
            </div>
            
            <!-- Navigation Links -->
            <div class="nav-links">
                <a href="/" class="nav-link active" data-page="dashboard">
                    <i class="fas fa-chart-line"></i>
                    <span>Dashboard</span>
                </a>
                <a href="/trading" class="nav-link" data-page="trading">
                    <i class="fas fa-exchange-alt"></i>
                    <span>Trading</span>
                </a>
                <a href="/config" class="nav-link" data-page="config">
                    <i class="fas fa-cog"></i>
                    <span>Config</span>
                </a>
                <a href="/logs" class="nav-link" data-page="logs">
                    <i class="fas fa-list-alt"></i>
                    <span>Logs</span>
                </a>
                <a href="/settings" class="nav-link" data-page="settings">
                    <i class="fas fa-tools"></i>
                    <span>Settings</span>
                </a>
            </div>
            
            <!-- Theme Toggle -->
            <div class="nav-controls">
                <button class="theme-toggle" id="theme-toggle" aria-label="Toggle theme">
                    <i class="fas fa-moon"></i>
                </button>
                <button class="refresh-btn" id="refresh-btn" aria-label="Refresh data">
                    <i class="fas fa-sync-alt"></i>
                </button>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="main-content">
        <!-- Loading Overlay -->
        <div class="loading-overlay" id="loading-overlay">
            <div class="loading-spinner">
                <i class="fas fa-spinner fa-spin"></i>
            </div>
            <div class="loading-text">Loading...</div>
        </div>
        
        <!-- Toast Notifications -->
        <div class="toast-container" id="toast-container"></div>
        
        <!-- Page Content -->
        <div class="page-content">
            {% block content %}{% endblock %}
        </div>
    </main>
    
    <!-- JavaScript Framework -->
    <script src="/static/js/api.js"></script>
    <script src="/static/js/dashboard.js"></script>
    <script src="/static/js/main.js"></script>
    
    {% block scripts %}{% endblock %}
</body>
</html>