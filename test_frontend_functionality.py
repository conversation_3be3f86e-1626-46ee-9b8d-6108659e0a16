#!/usr/bin/env python3
"""
Comprehensive frontend functionality test script
Tests all frontend pages and API integration
"""

import requests
import json
import time
from datetime import datetime

def test_frontend_pages():
    """Test all frontend pages"""
    frontend_base_url = "http://localhost:3000"
    
    print("Testing Frontend Pages...")
    print("=" * 50)
    
    pages = [
        ('/', 'Dashboard'),
        ('/trading', 'Trading Interface'),
        ('/config', 'Configuration'),
        ('/logs', 'Activity Logs'),
        ('/settings', 'System Settings')
    ]
    
    results = {}
    for path, name in pages:
        try:
            response = requests.get(f"{frontend_base_url}{path}", timeout=10)
            results[path] = {
                'status': response.status_code,
                'success': response.status_code == 200,
                'response_time': response.elapsed.total_seconds()
            }
            
            print(f"{name}: {response.status_code} ({'OK' if response.status_code == 200 else 'FAIL'}) ({response.elapsed.total_seconds():.3f}s)")
            
            if response.status_code == 200:
                # Check if it's a valid HTML page
                if 'html' in response.text.lower() and '<!DOCTYPE' in response.text:
                    print(f"  PASS: Valid HTML page loaded")
                else:
                    print(f"  FAIL: Not a valid HTML page")
            else:
                print(f"  FAIL: Page load failed: {response.text[:200]}")
                
        except Exception as e:
            results[path] = {
                'status': 'ERROR',
                'success': False,
                'error': str(e)
            }
            print(f"{name}: ERROR - {e}")
        
        print("-" * 30)
    
    return results

def test_api_endpoints():
    """Test all API endpoints through frontend proxy"""
    print("\nTesting API Endpoints Through Frontend Proxy...")
    print("=" * 50)
    
    frontend_base_url = "http://localhost:3000"
    endpoints = [
        ('/', 'Main/Health'),
        ('/balance', 'Balance'),
        ('/config', 'Configuration'),
        ('/emergency-stop', 'Emergency Stop')
    ]
    
    results = {}
    for endpoint, name in endpoints:
        try:
            response = requests.get(f"{frontend_base_url}/api{endpoint}", timeout=10)
            results[endpoint] = {
                'status': response.status_code,
                'success': response.status_code == 200,
                'response_time': response.elapsed.total_seconds()
            }
            
            print(f"{name}: {response.status_code} ({'OK' if response.status_code == 200 else 'FAIL'}) ({response.elapsed.total_seconds():.3f}s)")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"  PASS: Valid JSON response")
                    # Show some data summary
                    if 'balances' in data:
                        print(f"     Balance data: {len(data['balances'])} assets")
                    elif 'allowed_symbols' in data:
                        print(f"     Config data: {len(data['allowed_symbols'])} symbols")
                    elif 'binance_status' in data:
                        print(f"     Status: {data['binance_status']}")
                except:
                    print(f"  FAIL: Invalid JSON response")
            else:
                print(f"  FAIL: API call failed: {response.text[:200]}")
                
        except Exception as e:
            results[endpoint] = {
                'status': 'ERROR',
                'success': False,
                'error': str(e)
            }
            print(f"{name}: ERROR - {e}")
        
        print("-" * 30)
    
    return results

def test_navigation():
    """Test navigation between pages"""
    print("\nTesting Navigation...")
    print("=" * 50)
    
    frontend_base_url = "http://localhost:3000"
    
    # Test navigation flow
    navigation_flow = [
        ('/', 'Dashboard'),
        ('/trading', 'Trading Interface'),
        ('/config', 'Configuration'),
        ('/logs', 'Activity Logs'),
        ('/', 'Dashboard')  # Back to start
    ]
    
    results = {}
    last_response = None
    
    for path, name in navigation_flow:
        try:
            response = requests.get(f"{frontend_base_url}{path}", timeout=10)
            results[path] = {
                'status': response.status_code,
                'success': response.status_code == 200
            }
            
            print(f"{name}: {response.status_code} ({'OK' if response.status_code == 200 else 'FAIL'})")
            
            if response.status_code == 200:
                # Check if the page contains the expected title
                if name.lower() in response.text.lower():
                    print(f"  PASS: Contains expected title: {name}")
                else:
                    print(f"  WARN: Title verification not possible")
            else:
                print(f"  FAIL: Navigation failed")
                
        except Exception as e:
            results[path] = {
                'status': 'ERROR',
                'success': False,
                'error': str(e)
            }
            print(f"{name}: ERROR - {e}")
        
        print("-" * 30)
    
    return results

def test_performance():
    """Test performance metrics"""
    print("\nTesting Performance Metrics...")
    print("=" * 50)
    
    frontend_base_url = "http://localhost:3000"
    
    # Test load times
    pages = ['/', '/trading', '/config', '/logs']
    
    for page in pages:
        try:
            start_time = time.time()
            response = requests.get(f"{frontend_base_url}{page}", timeout=10)
            end_time = time.time()
            
            load_time = end_time - start_time
            response_time = response.elapsed.total_seconds()
            
            print(f"{page}:")
            print(f"  Total load time: {load_time:.3f}s")
            print(f"  Response time: {response_time:.3f}s")
            print(f"  Status: {response.status_code}")
            print(f"  Size: {len(response.text)} bytes")
            
            # Performance assessment
            if load_time < 2.0:
                print(f"  PASS: Fast loading")
            elif load_time < 5.0:
                print(f"  WARN: Moderate loading")
            else:
                print(f"  FAIL: Slow loading")
                
        except Exception as e:
            print(f"{page}: ERROR - {e}")
        
        print("-" * 30)

def generate_test_report(page_results, api_results, navigation_results):
    """Generate comprehensive test report"""
    print("\n[FRONTEND FUNCTIONALITY TEST REPORT]")
    print("=" * 50)
    
    # Page tests summary
    page_success = sum(1 for r in page_results.values() if r.get('success', False))
    page_total = len(page_results)
    
    print(f"\nFrontend Page Tests:")
    print(f"   Passed: {page_success}/{page_total}")
    print(f"   Success Rate: {(page_success/page_total)*100:.1f}%")
    
    # API tests summary
    api_success = sum(1 for r in api_results.values() if r.get('success', False))
    api_total = len(api_results)
    
    print(f"\nAPI Proxy Tests:")
    print(f"   Passed: {api_success}/{api_total}")
    print(f"   Success Rate: {(api_success/api_total)*100:.1f}%")
    
    # Navigation tests summary
    nav_success = sum(1 for r in navigation_results.values() if r.get('success', False))
    nav_total = len(navigation_results)
    
    print(f"\nNavigation Tests:")
    print(f"   Passed: {nav_success}/{nav_total}")
    print(f"   Success Rate: {(nav_success/nav_total)*100:.1f}%")
    
    # Overall assessment
    total_success = page_success + api_success + nav_success
    total_tests = page_total + api_total + nav_total
    
    print(f"\nOverall Assessment:")
    print(f"   Total Tests: {total_tests}")
    print(f"   Total Passed: {total_success}")
    print(f"   Overall Success Rate: {(total_success/total_tests)*100:.1f}%")
    
    # System status
    if total_success == total_tests:
        print("\nSystem Status: ALL TESTS PASSED")
        print("   Frontend: Fully operational")
        print("   API Proxy: Working correctly")
        print("   Navigation: Complete")
        return True
    elif total_success > total_tests * 0.8:
        print("\nSystem Status: MOSTLY OPERATIONAL")
        print("   Some issues detected, but system is functional")
        return False
    else:
        print("\nSystem Status: ISSUES DETECTED")
        print("   Multiple failures require attention")
        return False

def main():
    print("TradingView Bot Frontend Functionality Test")
    print("=" * 70)
    print(f"Test Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Test frontend pages
    page_results = test_frontend_pages()
    
    # Test API endpoints
    api_results = test_api_endpoints()
    
    # Test navigation
    navigation_results = test_navigation()
    
    # Test performance
    test_performance()
    
    # Generate report
    success = generate_test_report(page_results, api_results, navigation_results)
    
    # Portfolio analysis
    if '/balance' in api_results and api_results.get('/balance', {}).get('success', False):
        try:
            balance_response = requests.get("http://localhost:3000/api/balance", timeout=10)
            if balance_response.status_code == 200:
                balance_data = balance_response.json()
                
                print(f"\n[PORTFOLIO ANALYSIS]:")
                
                # Calculate total portfolio value
                total_value = 0
                for asset in balance_data['balances']:
                    asset_symbol = asset['asset']
                    free_amount = float(asset['free'])
                    locked_amount = float(asset['locked'])
                    total_amount = free_amount + locked_amount
                    
                    if asset_symbol == 'BTC':
                        value = total_amount * 50000  # Approximate BTC price
                        print(f"   {asset_symbol}: {total_amount:.8f} BTC ~ ${value:,.2f}")
                    elif asset_symbol == 'ETH':
                        value = total_amount * 3000  # Approximate ETH price
                        print(f"   {asset_symbol}: {total_amount:.8f} ETH ~ ${value:,.2f}")
                    elif asset_symbol in ['USDT', 'USD']:
                        print(f"   {asset_symbol}: {total_amount:.2f} = ${total_amount:,.2f}")
                    
                    total_value += total_amount
                
                print(f"   Total Portfolio Value: ${total_value:,.2f}")
        except Exception as e:
            print(f"\n[PORTFOLIO ANALYSIS]: Failed - {e}")
    
    print(f"\n[TEST COMPLETED]: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)