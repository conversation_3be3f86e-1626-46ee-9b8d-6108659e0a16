{% extends "base.html" %}

{% block title %}Configuration - TradingView <PERSON>t{% endblock %}

{% block content %}
<div class="container">
    <div class="page-header">
        <h1>Configuration</h1>
        <p class="subtitle">Manage trading settings and system configuration</p>
    </div>

    <!-- Emergency Stop Section -->
    <div class="card">
        <div class="card-header">
            <h3>Emergency Stop</h3>
            <div class="status-indicator">
                <span class="status-dot {{ 'active' if emergency_stop else 'inactive' }}"></span>
                <span class="status-text">{{ "EMERGENCY STOP ACTIVE" if emergency_stop else "System Active" }}</span>
            </div>
        </div>
        <div class="card-body">
            <p class="status-description">
                {{ "All trading is currently disabled. Toggle to resume trading." if emergency_stop else "System is running normally. Toggle to emergency stop to disable all trading." }}
            </p>
            <div class="button-group">
                <button id="emergencyToggleBtn" class="btn {{ 'danger' if emergency_stop else 'success' }} large" onclick="toggleEmergencyStop()">
                    {{ 'Enable Trading' if emergency_stop else 'Emergency Stop' }}
                </button>
            </div>
        </div>
    </div>

    <!-- Trading Configuration Section -->
    <div class="card">
        <div class="card-header">
            <h3>Trading Configuration</h3>
        </div>
        <div class="card-body">
            <div class="config-grid">
                <!-- Quantity Percentage -->
                <div class="config-item">
                    <label class="form-label">Quantity Percentage</label>
                    <div class="input-group">
                        <input type="number" id="quantityPercentage" class="form-input" value="{{ config.quantity_percentage }}" min="1" max="100" step="1">
                        <span class="input-addon">%</span>
                    </div>
                    <p class="form-help">Percentage of available balance to use per trade</p>
                </div>

                <!-- Allowed Symbols -->
                <div class="config-item">
                    <label class="form-label">Allowed Trading Symbols</label>
                    <div class="symbol-grid">
                        {% for symbol in config.allowed_symbols %}
                        <div class="symbol-item" data-symbol="{{ symbol }}">
                            <span class="symbol-name">{{ symbol }}</span>
                            <span class="symbol-status active">Active</span>
                        </div>
                        {% endfor %}
                    </div>
                    <button class="btn btn-outline small" onclick="refreshSymbols()">Refresh Symbols</button>
                </div>
            </div>

            <!-- Order Type Configuration -->
            <div class="config-section">
                <h4>Order Type Configuration</h4>
                <div class="order-config-grid">
                    <!-- BTCUSDT -->
                    <div class="order-config-item">
                        <h5>BTCUSDT</h5>
                        <div class="input-group">
                            <label>Min Order:</label>
                            <input type="text" class="form-input small" value="{{ config.fixed_quantities.BTCUSDT }}" readonly>
                        </div>
                        <div class="input-group">
                            <label>Max Order:</label>
                            <input type="text" class="form-input small" value="{{ config.max_quantities.BTCUSDT }}" readonly>
                        </div>
                    </div>

                    <!-- ETHUSDT -->
                    <div class="order-config-item">
                        <h5>ETHUSDT</h5>
                        <div class="input-group">
                            <label>Min Order:</label>
                            <input type="text" class="form-input small" value="{{ config.fixed_quantities.ETHUSDT }}" readonly>
                        </div>
                        <div class="input-group">
                            <label>Max Order:</label>
                            <input type="text" class="form-input small" value="{{ config.max_quantities.ETHUSDT }}" readonly>
                        </div>
                    </div>

                    <!-- BTCUSD -->
                    <div class="order-config-item">
                        <h5>BTCUSD</h5>
                        <div class="input-group">
                            <label>Min Order:</label>
                            <input type="text" class="form-input small" value="{{ config.fixed_quantities.BTCUSD }}" readonly>
                        </div>
                        <div class="input-group">
                            <label>Max Order:</label>
                            <input type="text" class="form-input small" value="{{ config.max_quantities.BTCUSD }}" readonly>
                        </div>
                    </div>

                    <!-- ETHUSD -->
                    <div class="order-config-item">
                        <h5>ETHUSD</h5>
                        <div class="input-group">
                            <label>Min Order:</label>
                            <input type="text" class="form-input small" value="{{ config.fixed_quantities.ETHUSD }}" readonly>
                        </div>
                        <div class="input-group">
                            <label>Max Order:</label>
                            <input type="text" class="form-input small" value="{{ config.max_quantities.ETHUSD }}" readonly>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- API Configuration Section -->
    <div class="card">
        <div class="card-header">
            <h3>API Configuration</h3>
        </div>
        <div class="card-body">
            <div class="config-grid">
                <div class="config-item">
                    <label class="form-label">API Base URL</label>
                    <input type="text" class="form-input" value="{{ api_url }}" readonly>
                    <p class="form-help">Backend API endpoint for trading operations</p>
                </div>
                <div class="config-item">
                    <label class="form-label">Connection Status</label>
                    <div class="connection-status">
                        <span class="status-dot {{ 'active' if api_status else 'inactive' }}"></span>
                        <span>{{ 'Connected' if api_status else 'Disconnected' }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Test Section -->
    <div class="card">
        <div class="card-header">
            <h3>System Testing</h3>
        </div>
        <div class="card-body">
            <div class="test-grid">
                <button class="btn btn-outline" onclick="testApiConnection()">
                    <i class="fas fa-plug"></i> Test API Connection
                </button>
                <button class="btn btn-outline" onclick="testBalance()">
                    <i class="fas fa-wallet"></i> Test Balance Retrieval
                </button>
                <button class="btn btn-outline" onclick="testConfiguration()">
                    <i class="fas fa-cog"></i> Test Configuration
                </button>
                <button class="btn btn-outline" onclick="runDiagnostics()">
                    <i class="fas fa-stethoscope"></i> Run Diagnostics
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Configuration Update Modal -->
<div id="configModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Configuration Update</h3>
            <button class="close" onclick="closeModal()">&times;</button>
        </div>
        <div class="modal-body">
            <div class="form-group">
                <label>Setting:</label>
                <p id="configSetting">Update trading configuration</p>
            </div>
            <div class="form-group">
                <label>Current Value:</label>
                <p id="configCurrentValue">Current value</p>
            </div>
            <div class="form-group">
                <label>New Value:</label>
                <input type="text" id="configNewValue" class="form-input">
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-outline" onclick="closeModal()">Cancel</button>
            <button class="btn btn-primary" onclick="updateConfiguration()">Update</button>
        </div>
    </div>
</div>

<!-- Test Results Modal -->
<div id="testModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Test Results</h3>
            <button class="close" onclick="closeTestModal()">&times;</button>
        </div>
        <div class="modal-body">
            <div id="testResults"></div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-primary" onclick="closeTestModal()">Close</button>
        </div>
    </div>
</div>

<!-- Success Modal -->
<div id="successModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Success</h3>
            <button class="close" onclick="closeSuccessModal()">&times;</button>
        </div>
        <div class="modal-body">
            <div class="success-message">
                <i class="fas fa-check-circle"></i>
                <p id="successText">Configuration updated successfully</p>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-primary" onclick="closeSuccessModal()">OK</button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="/static/js/config.js"></script>
<script>
// Initialize configuration page
document.addEventListener('DOMContentLoaded', function() {
    loadConfiguration();
    checkApiStatus();
    
    // Auto-refresh every 30 seconds
    setInterval(loadConfiguration, 30000);
});
</script>
{% endblock %}