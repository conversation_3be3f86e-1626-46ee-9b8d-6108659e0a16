{% extends "base.html" %}

{% block title %}Trading - Trading<PERSON><PERSON>w <PERSON>{% endblock %}

{% block head %}
<style>
    .trading-container {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .trading-panel {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 12px;
            padding: 1.5rem;
        display: flex;
        flex-direction: column;
    }
    
    .trading-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid var(--border-color);
    }
    
    .trading-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--text-primary);
    }
    
    .trade-form {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }
    
    .trade-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }
    
    .trade-buttons {
        display: flex;
        gap: 1rem;
        margin-top: 1rem;
    }
    
    .buy-btn {
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
        flex: 1;
    }
    
    .buy-btn:hover {
        background: linear-gradient(135deg, #059669, #047857);
        transform: translateY(-1px);
        box-shadow: 0 10px 25px rgba(16, 185, 129, 0.3);
    }
    
    .sell-btn {
        background: linear-gradient(135deg, #ef4444, #dc2626);
        color: white;
        flex: 1;
    }
    
    .sell-btn:hover {
        background: linear-gradient(135deg, #dc2626, #b91c1c);
        transform: translateY(-1px);
        box-shadow: 0 10px 25px rgba(239, 68, 68, 0.3);
    }
    
    .symbol-selector {
        display: flex;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }
    
    .symbol-btn {
        flex: 1;
        padding: 0.75rem;
        border: 2px solid var(--border-color);
        background: var(--card-bg);
        color: var(--text-secondary);
        border-radius: 8px;
        cursor: pointer;
        transition: all var(--transition-fast);
        font-weight: 500;
    }
    
    .symbol-btn:hover {
        border-color: var(--accent-primary);
        color: var(--accent-primary);
        background: var(--bg-tertiary);
    }
    
    .symbol-btn.active {
        border-color: var(--accent-primary);
        background: var(--accent-primary);
        color: white;
    }
    
    .quick-trade-btn {
        padding: 1rem;
        border: 1px solid var(--border-color);
        background: var(--bg-tertiary);
        color: var(--text-primary);
        border-radius: 8px;
        cursor: pointer;
        transition: all var(--transition-fast);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        font-weight: 500;
    }
    
    .quick-trade-btn:hover {
        background: var(--border-color);
        transform: translateY(-2px);
    }
    
    .quick-trade-btn.buy {
        border-color: #10b981;
        color: #10b981;
    }
    
    .quick-trade-btn.buy:hover {
        background: rgba(16, 185, 129, 0.1);
    }
    
    .quick-trade-btn.sell {
        border-color: #ef4444;
        color: #ef4444;
    }
    
    .quick-trade-btn.sell:hover {
        background: rgba(239, 68, 68, 0.1);
    }
    
    .trade-info {
        background: var(--bg-tertiary);
        border-radius: 8px;
        padding: 1rem;
        margin-top: 1rem;
    }
    
    .info-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.5rem;
    }
    
    .info-label {
        color: var(--text-secondary);
        font-size: 0.875rem;
    }
    
    .info-value {
        font-weight: 500;
        color: var(--text-primary);
    }
    
    .order-history {
        max-height: 400px;
        overflow-y: auto;
    }
    
    .order-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 1rem;
        border-bottom: 1px solid var(--border-color);
        transition: all var(--transition-fast);
    }
    
    .order-item:hover {
        background: var(--bg-tertiary);
        margin: 0 -1.5rem;
        padding-left: 1.5rem;
        padding-right: 1.5rem;
        border-radius: 8px;
    }
    
    .order-details {
        display: flex;
        flex-direction: column;
    }
    
    .order-symbol {
        font-weight: 500;
        color: var(--text-primary);
    }
    
    .order-time {
        font-size: 0.75rem;
        color: var(--text-secondary);
    }
    
    .order-amount {
        text-align: right;
    }
    
    .order-price {
        font-weight: 500;
        color: var(--text-primary);
    }
    
    .order-side {
        font-size: 0.75rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }
    
    .order-status {
        font-size: 0.75rem;
        font-weight: 500;
    }
    
    @media (max-width: 768px) {
        .trading-container {
            grid-template-columns: 1fr;
        }
        
        .trade-row {
            grid-template-columns: 1fr;
        }
        
        .trade-buttons {
            flex-direction: column;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Header -->
<div class="page-header">
    <div class="header-content">
        <h1 class="page-title">Trading Interface</h1>
        <p class="page-subtitle">Manual trading controls and real-time market access</p>
    </div>
    <div class="header-actions">
        <button class="btn btn-secondary" id="refresh-trading">
            <i class="fas fa-sync-alt"></i> Refresh
        </button>
    </div>
</div>

<!-- Trading Container -->
<div class="trading-container">
    <!-- Quick Trading Panel -->
    <div class="trading-panel">
        <div class="trading-header">
            <h2 class="trading-title">Quick Trade</h2>
            <span class="badge badge-info" id="trading-status">Ready</span>
        </div>
        
        <!-- Symbol Selection -->
        <div class="symbol-selector">
            <button class="symbol-btn active" data-symbol="BTCUSDT">BTCUSDT</button>
            <button class="symbol-btn" data-symbol="BTCUSD">BTCUSD</button>
            <button class="symbol-btn" data-symbol="ETHUSDT">ETHUSDT</button>
            <button class="symbol-btn" data-symbol="ETHUSD">ETHUSD</button>
        </div>
        
        <!-- Trade Form -->
        <form class="trade-form" id="trade-form">
            <div class="form-group">
                <label class="form-label">Trade Type</label>
                <div class="trade-row">
                    <div>
                        <label class="form-label" style="font-size: 0.875rem; margin-bottom: 0.25rem;">Quantity</label>
                        <input type="number" class="form-control" id="trade-quantity" 
                               placeholder="0.001" step="0.000001" min="0.000001">
                    </div>
                    <div>
                        <label class="form-label" style="font-size: 0.875rem; margin-bottom: 0.25rem;">Order Type</label>
                        <select class="form-select" id="order-type">
                            <option value="market">Market Order</option>
                            <option value="limit">Limit Order</option>
                            <option value="percentage">Percentage of Balance</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="form-group" id="price-group" style="display: none;">
                <label class="form-label">Price (USD)</label>
                <input type="number" class="form-control" id="trade-price" 
                       placeholder="50000.00" step="0.01">
            </div>
            
            <div class="form-group" id="percentage-group">
                <label class="form-label">Percentage of Balance</label>
                <input type="range" class="form-range" id="balance-percentage" 
                       min="1" max="100" value="50" style="margin-bottom: 0.5rem;">
                <div class="d-flex justify-content-between">
                    <small class="text-secondary">1%</small>
                    <small class="text-secondary fw-bold" id="percentage-value">50%</small>
                    <small class="text-secondary">100%</small>
                </div>
            </div>
            
            <div class="trade-info">
                <div class="info-row">
                    <span class="info-label">Current Balance:</span>
                    <span class="info-value" id="current-balance">$0.00</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Estimated Quantity:</span>
                    <span class="info-value" id="estimated-quantity">0.000000</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Market Price:</span>
                    <span class="info-value" id="market-price">$0.00</span>
                </div>
            </div>
            
            <div class="trade-buttons">
                <button type="submit" class="btn buy-btn" id="buy-btn">
                    <i class="fas fa-arrow-up"></i> Buy
                </button>
                <button type="submit" class="btn sell-btn" id="sell-btn">
                    <i class="fas fa-arrow-down"></i> Sell
                </button>
            </div>
        </form>
    </div>
    
    <!-- Recent Activity Panel -->
    <div class="trading-panel">
        <div class="trading-header">
            <h2 class="trading-title">Recent Activity</h2>
            <button class="btn btn-sm btn-secondary" onclick="loadRecentTrades()">
                <i class="fas fa-sync-alt"></i>
            </button>
        </div>
        
        <!-- Quick Trade Buttons -->
        <div style="margin-bottom: 1.5rem;">
            <div class="symbol-selector">
                <button class="quick-trade-btn buy" onclick="quickTrade('BUY', 'BTCUSDT')">
                    <i class="fas fa-arrow-up"></i> Buy 0.001 BTC
                </button>
                <button class="quick-trade-btn sell" onclick="quickTrade('SELL', 'BTCUSDT')">
                    <i class="fas fa-arrow-down"></i> Sell 0.001 BTC
                </button>
            </div>
        </div>
        
        <!-- Order History -->
        <div class="order-history" id="order-history">
            <div class="order-item">
                <div class="order-details">
                    <div class="order-symbol">BTCUSDT</div>
                    <div class="order-time">Just now</div>
                </div>
                <div class="order-amount">
                    <div class="order-price">$56,850.00</div>
                    <div class="order-side">BUY</div>
                </div>
                <div class="order-amount">
                    <div class="order-side">0.001</div>
                    <div class="order-status badge badge-success">FILLED</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Order History Table -->
<div class="trading-panel" style="margin-top: 1.5rem;">
    <div class="trading-header">
        <h2 class="trading-title">Order History</h2>
        <div class="header-actions">
            <select class="form-select form-select-sm" id="order-filter">
                <option value="all">All Orders</option>
                <option value="buy">Buy Orders</option>
                <option value="sell">Sell Orders</option>
                <option value="filled">Filled Orders</option>
                <option value="pending">Pending Orders</option>
            </select>
        </div>
    </div>
    
    <div class="table-container">
        <table class="data-table">
            <thead>
                <tr>
                    <th>Time</th>
                    <th>Symbol</th>
                    <th>Type</th>
                    <th>Side</th>
                    <th>Quantity</th>
                    <th>Price</th>
                    <th>Status</th>
                    <th>P&L</th>
                </tr>
            </thead>
            <tbody id="order-history-tbody">
                <tr>
                    <td colspan="8" class="text-center">Loading order history...</td>
                </tr>
            </tbody>
        </table>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Trading functionality
    let currentSymbol = 'BTCUSDT';
    let currentSide = 'BUY';
    let tradingData = {
        balances: {},
        prices: {},
        orders: []
    };

    document.addEventListener('DOMContentLoaded', function() {
        initializeTradingInterface();
        loadTradingData();
        
        // Set up auto-refresh
        setInterval(loadTradingData, 30000); // Refresh every 30 seconds
    });

    async function initializeTradingInterface() {
        console.log('Initializing Trading Interface...');
        
        // Set up symbol selection
        const symbolButtons = document.querySelectorAll('.symbol-btn');
        symbolButtons.forEach(btn => {
            btn.addEventListener('click', function() {
                symbolButtons.forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                currentSymbol = this.dataset.symbol;
                updateTradingInfo();
            });
        });
        
        // Set up trade form
        const tradeForm = document.getElementById('trade-form');
        tradeForm.addEventListener('submit', handleTradeSubmit);
        
        // Set up order type change
        const orderType = document.getElementById('order-type');
        orderType.addEventListener('change', function() {
            const priceGroup = document.getElementById('price-group');
            const percentageGroup = document.getElementById('percentage-group');
            
            if (this.value === 'limit') {
                priceGroup.style.display = 'block';
                percentageGroup.style.display = 'none';
            } else if (this.value === 'percentage') {
                priceGroup.style.display = 'none';
                percentageGroup.style.display = 'block';
            } else {
                priceGroup.style.display = 'none';
                percentageGroup.style.display = 'none';
            }
        });
        
        // Set up percentage slider
        const percentageSlider = document.getElementById('balance-percentage');
        const percentageValue = document.getElementById('percentage-value');
        
        percentageSlider.addEventListener('input', function() {
            percentageValue.textContent = this.value + '%';
            updateEstimatedQuantity();
        });
        
        // Set up refresh button
        const refreshBtn = document.getElementById('refresh-trading');
        refreshBtn.addEventListener('click', loadTradingData);
        
        // Set up order filter
        const orderFilter = document.getElementById('order-filter');
        orderFilter.addEventListener('change', loadOrderHistory);
        
        // Set up keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.key === 'b' || e.key === 'B') {
                currentSide = 'BUY';
                updateTradeButtons();
            } else if (e.key === 's' || e.key === 'S') {
                currentSide = 'SELL';
                updateTradeButtons();
            }
        });
    }

    async function loadTradingData() {
        try {
            // Load balance and trading data in parallel
            const [balanceResponse, statsResponse] = await Promise.all([
                api.getBalance(),
                api.getTradingStats()
            ]);
            
            if (balanceResponse.success) {
                tradingData.balances = balanceResponse.data.balances;
                updateAccountBalance();
            }
            
            if (statsResponse.success) {
                tradingData.orders = statsResponse.data.recentTrades || [];
                updateOrderHistory();
            }
            
            // Load market prices
            await loadMarketPrices();
            
        } catch (error) {
            console.error('Error loading trading data:', error);
            showToast('Error loading trading data', 'error');
        }
    }

    async function loadMarketPrices() {
        try {
            const symbols = ['BTCUSDT', 'BTCUSD', 'ETHUSDT', 'ETHUSD'];
            
            for (const symbol of symbols) {
                const response = await api.getMarketPrice(symbol);
                if (response.success) {
                    tradingData.prices[symbol] = response.data.price;
                }
            }
            
            updateTradingInfo();
            
        } catch (error) {
            console.error('Error loading market prices:', error);
        }
    }

    function updateAccountBalance() {
        let totalBalance = 0;
        
        // Calculate total portfolio value in USD
        Object.keys(tradingData.prices).forEach(symbol => {
            const baseAsset = symbol.replace('USDT', '').replace('USD', '');
            const balanceData = tradingData.balances.find(b => b.asset === baseAsset);
            
            if (balanceData && tradingData.prices[symbol]) {
                const balance = parseFloat(balanceData.free);
                const value = balance * tradingData.prices[symbol];
                totalBalance += value;
            }
        });
        
        // Add USDT balance
        const usdtBalance = tradingData.balances.find(b => b.asset === 'USDT');
        if (usdtBalance) {
            totalBalance += parseFloat(usdtBalance.free);
        }
        
        document.getElementById('current-balance').textContent = formatCurrency(totalBalance);
    }

    function updateTradingInfo() {
        // Update market price
        const marketPrice = tradingData.prices[currentSymbol] || 0;
        document.getElementById('market-price').textContent = formatCurrency(marketPrice);
        
        // Update estimated quantity based on percentage
        updateEstimatedQuantity();
    }

    function updateEstimatedQuantity() {
        const percentage = parseInt(document.getElementById('balance-percentage').value);
        const marketPrice = tradingData.prices[currentSymbol] || 0;
        
        if (marketPrice > 0) {
            let usableBalance = 0;
            
            // Calculate usable balance based on trade side
            if (currentSide === 'BUY') {
                const quoteAsset = currentSymbol.includes('USDT') ? 'USDT' : 'USD';
                const balanceData = tradingData.balances.find(b => b.asset === quoteAsset);
                if (balanceData) {
                    usableBalance = parseFloat(balanceData.free) * (percentage / 100);
                }
            } else {
                const baseAsset = currentSymbol.replace('USDT', '').replace('USD', '');
                const balanceData = tradingData.balances.find(b => b.asset === baseAsset);
                if (balanceData) {
                    usableBalance = parseFloat(balanceData.free) * (percentage / 100);
                }
            }
            
            const estimatedQuantity = marketPrice > 0 ? (usableBalance / marketPrice).toFixed(8) : '0.000000';
            document.getElementById('estimated-quantity').textContent = estimatedQuantity;
        }
    }

    function updateTradeButtons() {
        const buyBtn = document.getElementById('buy-btn');
        const sellBtn = document.getElementById('sell-btn');
        
        if (currentSide === 'BUY') {
            buyBtn.style.transform = 'scale(1.05)';
            sellBtn.style.transform = 'scale(1)';
        } else {
            sellBtn.style.transform = 'scale(1.05)';
            buyBtn.style.transform = 'scale(1)';
        }
    }

    async function handleTradeSubmit(event) {
        event.preventDefault();
        
        const formData = new FormData(event.target);
        const quantity = document.getElementById('trade-quantity').value;
        const orderType = document.getElementById('order-type').value;
        const percentage = document.getElementById('balance-percentage').value;
        
        // Validate form
        if (!quantity && orderType !== 'percentage') {
            showToast('Please enter a quantity', 'error');
            return;
        }
        
        // Show loading state
        showTradingStatus('Processing...', 'warning');
        
        try {
            let tradeData = {
                symbol: currentSymbol,
                action: currentSide
            };
            
            if (orderType === 'percentage') {
                // Use percentage-based calculation
                const calcResponse = await api.calculateQuantity(currentSymbol, currentSide, parseInt(percentage));
                if (calcResponse.success) {
                    tradeData.quantity = calcResponse.data.calculatedQuantity;
                } else {
                    throw new Error(calcResponse.error);
                }
            } else if (orderType === 'limit') {
                const price = document.getElementById('trade-price').value;
                if (!price) {
                    showToast('Please enter a price', 'error');
                    return;
                }
                tradeData.price = price;
                tradeData.quantity = quantity;
            } else {
                tradeData.quantity = quantity;
            }
            
            // Execute trade via test endpoint
            const response = await api.executeTestTrade(tradeData);
            
            if (response.success) {
                showTradingStatus('Order executed successfully!', 'success');
                showToast(`${currentSide} order executed for ${currentSymbol}`, 'success');
                
                // Refresh data
                await loadTradingData();
                
                // Reset form
                event.target.reset();
                updateEstimatedQuantity();
                
            } else {
                throw new Error(response.error);
            }
            
        } catch (error) {
            console.error('Trade execution error:', error);
            showTradingStatus('Order failed', 'error');
            showToast('Order execution failed: ' + error.message, 'error');
        } finally {
            setTimeout(() => {
                showTradingStatus('Ready', 'info');
            }, 3000);
        }
    }

    function showTradingStatus(message, type) {
        const statusBadge = document.getElementById('trading-status');
        statusBadge.textContent = message;
        statusBadge.className = `badge badge-${type}`;
    }

    async function quickTrade(side, symbol) {
        currentSymbol = symbol;
        currentSide = side;
        
        // Update UI
        document.querySelectorAll('.symbol-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.symbol === symbol);
        });
        
        updateTradeButtons();
        updateTradingInfo();
        
        // Auto-submit with default quantity
        const tradeForm = document.getElementById('trade-form');
        const quantityInput = document.getElementById('trade-quantity');
        quantityInput.value = '0.001'; // Default quantity
        
        // Trigger form submission
        const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
        tradeForm.dispatchEvent(submitEvent);
    }

    function loadRecentTrades() {
        const orderHistory = document.getElementById('order-history');
        
        // Simulate loading
        orderHistory.innerHTML = '<div style="text-align: center; padding: 2rem;">Loading recent trades...</div>';
        
        setTimeout(() => {
            // Mock recent trades
            const trades = [
                {
                    time: new Date().toISOString(),
                    symbol: 'BTCUSDT',
                    side: 'BUY',
                    quantity: '0.001',
                    price: '56850.00',
                    status: 'FILLED'
                },
                {
                    time: new Date(Date.now() - 5 * 60000).toISOString(),
                    symbol: 'BTCUSD',
                    side: 'SELL',
                    quantity: '0.001',
                    price: '56845.00',
                    status: 'FILLED'
                }
            ];
            
            orderHistory.innerHTML = trades.map(trade => `
                <div class="order-item">
                    <div class="order-details">
                        <div class="order-symbol">${trade.symbol}</div>
                        <div class="order-time">${formatDateTime(trade.time)}</div>
                    </div>
                    <div class="order-amount">
                        <div class="order-price">$${formatNumber(trade.price)}</div>
                        <div class="order-side ${trade.side === 'BUY' ? 'text-success' : 'text-danger'}">${trade.side}</div>
                    </div>
                    <div class="order-amount">
                        <div class="order-side">${trade.quantity}</div>
                        <div class="order-status badge badge-success">${trade.status}</div>
                    </div>
                </div>
            `).join('');
            
        }, 1000);
    }

    function loadOrderHistory() {
        const tbody = document.getElementById('order-history-tbody');
        const filter = document.getElementById('order-filter').value;
        
        tbody.innerHTML = '<tr><td colspan="8" class="text-center">Loading order history...</td></tr>';
        
        setTimeout(() => {
            // Mock order history data
            const orders = [
                {
                    time: new Date(Date.now() - 10 * 60000).toISOString(),
                    symbol: 'BTCUSDT',
                    type: 'MARKET',
                    side: 'BUY',
                    quantity: '0.001',
                    price: '56850.00',
                    status: 'FILLED',
                    pnl: '0.00'
                },
                {
                    time: new Date(Date.now() - 25 * 60000).toISOString(),
                    symbol: 'BTCUSD',
                    type: 'MARKET',
                    side: 'SELL',
                    quantity: '0.001',
                    price: '56845.00',
                    status: 'FILLED',
                    pnl: '-$0.50'
                },
                {
                    time: new Date(Date.now() - 45 * 60000).toISOString(),
                    symbol: 'ETHUSDT',
                    type: 'LIMIT',
                    side: 'BUY',
                    quantity: '0.01',
                    price: '3200.00',
                    status: 'PENDING',
                    pnl: '0.00'
                }
            ];
            
            // Apply filter
            let filteredOrders = orders;
            if (filter !== 'all') {
                filteredOrders = orders.filter(order => {
                    if (filter === 'buy') return order.side === 'BUY';
                    if (filter === 'sell') return order.side === 'SELL';
                    if (filter === 'filled') return order.status === 'FILLED';
                    if (filter === 'pending') return order.status === 'PENDING';
                    return true;
                });
            }
            
            tbody.innerHTML = filteredOrders.map(order => `
                <tr>
                    <td>${formatDateTime(order.time)}</td>
                    <td>${order.symbol}</td>
                    <td>${order.type}</td>
                    <td>
                        <span class="badge ${order.side === 'BUY' ? 'badge-buy' : 'badge-sell'}">
                            ${order.side}
                        </span>
                    </td>
                    <td>${order.quantity}</td>
                    <td>$${formatNumber(order.price)}</td>
                    <td>
                        <span class="badge ${order.status === 'FILLED' ? 'badge-success' : 'badge-warning'}">
                            ${order.status}
                        </span>
                    </td>
                    <td class="${order.pnl.startsWith('-') ? 'text-danger' : 'text-success'}">
                        ${order.pnl}
                    </td>
                </tr>
            `).join('');
            
        }, 1000);
    }

    // Utility functions
    function formatCurrency(amount) {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount);
    }

    function formatNumber(num) {
        return new Intl.NumberFormat('en-US').format(num);
    }

    function formatDateTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            month: 'short',
            day: 'numeric'
        });
    }

    // Make functions globally available
    window.quickTrade = quickTrade;
    window.loadRecentTrades = loadRecentTrades;
    window.loadOrderHistory = loadOrderHistory;
</script>
{% endblock %}