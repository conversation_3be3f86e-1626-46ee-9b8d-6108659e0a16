#!/usr/bin/env python3
"""
Test script to verify frontend API connectivity to local backend
"""

import requests
import json
from datetime import datetime

def test_frontend_api_connectivity():
    """Test frontend API proxy to local backend"""
    frontend_base_url = "http://localhost:3000"
    backend_base_url = "http://localhost:8000"
    
    print("Testing Frontend API Proxy Connectivity...")
    print("=" * 50)
    
    endpoints = [
        ('main', '/'),
        ('balance', '/balance'),
        ('config', '/config'),
        ('health', '/'),  # Using main endpoint as health check
        ('test', '/test')
    ]
    
    results = {}
    
    for name, endpoint in endpoints:
        try:
            # Test direct backend call
            direct_response = requests.get(f"{backend_base_url}{endpoint}", timeout=5)
            direct_success = direct_response.status_code == 200
            
            # Test frontend proxy call
            proxy_response = requests.get(f"{frontend_base_url}/api{endpoint}", timeout=5)
            proxy_success = proxy_response.status_code == 200
            
            results[name] = {
                'direct_backend': direct_response.status_code,
                'direct_success': direct_success,
                'frontend_proxy': proxy_response.status_code,
                'proxy_success': proxy_success,
                'response_time': proxy_response.elapsed.total_seconds()
            }
            
            print(f"{name.upper()}:")
            print(f"  Direct Backend: {direct_response.status_code} ({'OK' if direct_success else 'FAIL'})")
            print(f"  Frontend Proxy: {proxy_response.status_code} ({'OK' if proxy_success else 'FAIL'}) ({proxy_response.elapsed.total_seconds():.3f}s)")
            
            if proxy_success:
                try:
                    data = proxy_response.json()
                    print(f"  Response: {json.dumps(data, indent=2)[:100]}...")
                except:
                    print(f"  Response: {proxy_response.text[:100]}...")
            else:
                print(f"  Error: {proxy_response.text[:100]}...")
            
            print("-" * 30)
            
        except Exception as e:
            results[name] = {
                'direct_backend': 'ERROR',
                'direct_success': False,
                'frontend_proxy': 'ERROR',
                'proxy_success': False,
                'error': str(e)
            }
            print(f"{name.upper()}: ERROR - {e}")
            print("-" * 30)
    
    return results

def test_frontend_pages():
    """Test frontend page loading"""
    print("\nTesting Frontend Page Loading...")
    print("=" * 50)
    
    frontend_base_url = "http://localhost:3000"
    pages = [
        ('/', 'Dashboard'),
        ('/trading', 'Trading Interface'),
        ('/config', 'Configuration'),
        ('/logs', 'Activity Logs')
    ]
    
    for path, name in pages:
        try:
            response = requests.get(f"{frontend_base_url}{path}", timeout=10)
            print(f"{name}: {response.status_code} ({'OK' if response.status_code == 200 else 'FAIL'})")
            
            if response.status_code == 200:
                # Check if it's a valid HTML page
                if 'html' in response.text.lower() and '<!DOCTYPE' in response.text:
                    print(f"  ✅ Valid HTML page loaded")
                else:
                    print(f"  ❌ Not a valid HTML page")
            else:
                print(f"  ❌ Page load failed: {response.text[:200]}")
                
        except Exception as e:
            print(f"{name}: ERROR - {e}")
        
        print("-" * 30)

def main():
    print(f"Frontend API Connectivity Test")
    print(f"Test Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 70)
    
    # Test API connectivity
    api_results = test_frontend_api_connectivity()
    
    # Test frontend pages
    test_frontend_pages()
    
    # Summary
    print("\n[CONNECTIVITY TEST SUMMARY]")
    print("=" * 50)
    
    proxy_success = sum(1 for r in api_results.values() if r.get('proxy_success', False))
    total_tests = len(api_results)
    
    print(f"API Proxy Tests: {proxy_success}/{total_tests} successful")
    print(f"Success Rate: {(proxy_success/total_tests)*100:.1f}%")
    
    if proxy_success == total_tests:
        print("ALL API proxy connections working correctly")
        return True
    else:
        print("Some API proxy connections failed")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)