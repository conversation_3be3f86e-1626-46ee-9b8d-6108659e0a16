/**
 * Activity Logs Management JavaScript
 * Handles activity monitoring, filtering, and real-time updates
 */

class ActivityLogger {
    constructor() {
        this.api = window.api;
        this.activities = [];
        this.filteredActivities = [];
        this.filters = {
            type: '',
            symbol: '',
            status: '',
            timeRange: '24'
        };
        this.isLoading = false;
    }

    /**
     * Load activity logs from backend
     */
    async loadActivityLogs() {
        if (this.isLoading) return;
        
        this.isLoading = true;
        this.showLoadingIndicator();
        
        try {
            const response = await this.api.getRecentActivity(200);
            if (response.success) {
                this.activities = response.data;
                this.applyFilters();
                this.renderActivityTimeline();
                this.updateLogCount();
                this.updateLastUpdateTime();
            } else {
                throw new Error(response.error);
            }
        } catch (error) {
            this.showNotification('Failed to load activity logs: ' + error.message, 'error');
            this.showNoActivities();
        } finally {
            this.isLoading = false;
            this.hideLoadingIndicator();
        }
    }

    /**
     * Apply current filters to activities
     */
    applyFilters() {
        const now = new Date();
        const hours = parseInt(this.filters.timeRange);
        const cutoffTime = new Date(now.getTime() - hours * 60 * 60 * 1000);
        
        this.filteredActivities = this.activities.filter(activity => {
            // Time filter
            const activityTime = new Date(activity.timestamp);
            if (activityTime < cutoffTime) return false;
            
            // Type filter
            if (this.filters.type && activity.type !== this.filters.type) return false;
            
            // Symbol filter
            if (this.filters.symbol && activity.symbol !== this.filters.symbol) return false;
            
            // Status filter
            if (this.filters.status && activity.status !== this.filters.status) return false;
            
            return true;
        });
    }

    /**
     * Render activity timeline
     */
    renderActivityTimeline() {
        const timeline = document.getElementById('activityTimeline');
        timeline.innerHTML = '';

        if (this.filteredActivities.length === 0) {
            this.showNoActivities();
            return;
        }

        this.hideNoActivities();

        // Group activities by date
        const groupedActivities = this.groupActivitiesByDate(this.filteredActivities);
        
        Object.keys(groupedActivities).forEach(date => {
            const dateGroup = document.createElement('div');
            dateGroup.className = 'date-group';
            
            // Date header
            const dateHeader = document.createElement('div');
            dateHeader.className = 'date-header';
            const formattedDate = this.formatDate(date);
            dateHeader.innerHTML = `
                <h4>${formattedDate}</h4>
                <span>${groupedActivities[date].length} activities</span>
            `;
            dateGroup.appendChild(dateHeader);
            
            // Activities for this date
            groupedActivities[date].forEach(activity => {
                const activityItem = this.createActivityItem(activity);
                dateGroup.appendChild(activityItem);
            });
            
            timeline.appendChild(dateGroup);
        });
    }

    /**
     * Group activities by date
     */
    groupActivitiesByDate(activities) {
        const grouped = {};
        
        activities.forEach(activity => {
            const date = activity.timestamp.split('T')[0];
            if (!grouped[date]) {
                grouped[date] = [];
            }
            grouped[date].push(activity);
        });
        
        return grouped;
    }

    /**
     * Create activity item element
     */
    createActivityItem(activity) {
        const item = document.createElement('div');
        item.className = `activity-item ${activity.type.toLowerCase()} ${activity.status.toLowerCase()}`;
        item.onclick = () => this.showActivityDetails(activity);
        
        const icon = this.getActivityIcon(activity.type);
        const formattedTime = this.formatTime(activity.timestamp);
        
        item.innerHTML = `
            <div class="activity-time">${formattedTime}</div>
            <div class="activity-icon ${activity.type.toLowerCase()}">
                <i class="${icon}"></i>
            </div>
            <div class="activity-content">
                <div class="activity-main">
                    <span class="activity-type">${activity.type}</span>
                    ${activity.symbol ? `<span class="activity-symbol">${activity.symbol}</span>` : ''}
                </div>
                <div class="activity-message">${activity.message || 'No message'}</div>
                ${activity.quantity && activity.price ? `
                    <div class="activity-details">
                        <span>Qty: ${activity.quantity}</span>
                        <span>Price: $${activity.price}</span>
                    </div>
                ` : ''}
            </div>
            <div class="activity-status">
                <span class="status-dot ${activity.status.toLowerCase()}"></span>
                <span>${activity.status}</span>
            </div>
        `;
        
        return item;
    }

    /**
     * Get icon for activity type
     */
    getActivityIcon(type) {
        const icons = {
            'BUY': 'fas fa-arrow-up',
            'SELL': 'fas fa-arrow-down',
            'SYSTEM': 'fas fa-cog',
            'ALERT': 'fas fa-bell',
            'TRADE': 'fas fa-exchange-alt'
        };
        return icons[type] || 'fas fa-info-circle';
    }

    /**
     * Show activity details modal
     */
    showActivityDetails(activity) {
        const modal = document.getElementById('activityModal');
        const details = document.getElementById('activityDetails');
        
        details.innerHTML = `
            <div class="activity-detail-grid">
                <div class="detail-item">
                    <label>Activity ID:</label>
                    <p>${activity.id || 'N/A'}</p>
                </div>
                <div class="detail-item">
                    <label>Type:</label>
                    <p>${activity.type}</p>
                </div>
                <div class="detail-item">
                    <label>Symbol:</label>
                    <p>${activity.symbol || 'N/A'}</p>
                </div>
                <div class="detail-item">
                    <label>Timestamp:</label>
                    <p>${new Date(activity.timestamp).toLocaleString()}</p>
                </div>
                <div class="detail-item">
                    <label>Status:</label>
                    <p><span class="status-badge ${activity.status.toLowerCase()}">${activity.status}</span></p>
                </div>
                <div class="detail-item">
                    <label>Action:</label>
                    <p>${activity.action || 'N/A'}</p>
                </div>
                ${activity.quantity ? `
                <div class="detail-item">
                    <label>Quantity:</label>
                    <p>${activity.quantity}</p>
                </div>
                ` : ''}
                ${activity.price ? `
                <div class="detail-item">
                    <label>Price:</label>
                    <p>$${activity.price}</p>
                </div>
                ` : ''}
                <div class="detail-item full-width">
                    <label>Message:</label>
                    <p>${activity.message || 'No message'}</p>
                </div>
            </div>
        `;
        
        modal.style.display = 'block';
    }

    /**
     * Filter logs based on current filter values
     */
    filterLogs() {
        this.filters.type = document.getElementById('activityType').value;
        this.filters.symbol = document.getElementById('activitySymbol').value;
        this.filters.status = document.getElementById('activityStatus').value;
        this.filters.timeRange = document.getElementById('timeRange').value;
        
        this.applyFilters();
        this.renderActivityTimeline();
        this.updateLogCount();
    }

    /**
     * Clear all filters
     */
    clearFilters() {
        this.filters = {
            type: '',
            symbol: '',
            status: '',
            timeRange: '24'
        };
        
        // Reset filter inputs
        document.getElementById('activityType').value = '';
        document.getElementById('activitySymbol').value = '';
        document.getElementById('activityStatus').value = '';
        document.getElementById('timeRange').value = '24';
        
        this.applyFilters();
        this.renderActivityTimeline();
        this.updateLogCount();
    }

    /**
     * Refresh logs
     */
    refreshLogs() {
        this.loadActivityLogs();
    }

    /**
     * Export logs
     */
    exportLogs() {
        document.getElementById('exportModal').style.display = 'block';
    }

    /**
     * Perform export
     */
    async performExport() {
        const format = document.getElementById('exportFormat').value;
        const startDate = document.getElementById('exportStartDate').value;
        const endDate = document.getElementById('exportEndDate').value;
        const includeDetails = document.getElementById('exportIncludeDetails').checked;
        const includeStats = document.getElementById('exportIncludeStats').checked;
        
        try {
            const exportData = this.prepareExportData(startDate, endDate, includeDetails, includeStats);
            
            switch (format) {
                case 'json':
                    this.exportJson(exportData);
                    break;
                case 'csv':
                    this.exportCsv(exportData);
                    break;
                case 'txt':
                    this.exportText(exportData);
                    break;
                default:
                    throw new Error('Unsupported export format');
            }
            
            closeExportModal();
            this.showNotification('Logs exported successfully', 'success');
        } catch (error) {
            this.showNotification('Failed to export logs: ' + error.message, 'error');
        }
    }

    /**
     * Prepare export data
     */
    prepareExportData(startDate, endDate, includeDetails, includeStats) {
        let filteredData = this.activities;
        
        // Apply date filters
        if (startDate) {
            filteredData = filteredData.filter(activity => 
                activity.timestamp >= startDate + 'T00:00:00.000Z'
            );
        }
        
        if (endDate) {
            filteredData = filteredData.filter(activity => 
                activity.timestamp <= endDate + 'T23:59:59.999Z'
            );
        }
        
        return {
            activities: filteredData,
            summary: includeStats ? this.calculateSummary(filteredData) : null,
            exportTime: new Date().toISOString(),
            filterCriteria: {
                startDate,
                endDate,
                type: this.filters.type,
                symbol: this.filters.symbol,
                status: this.filters.status
            }
        };
    }

    /**
     * Calculate summary statistics
     */
    calculateSummary(activities) {
        const summary = {
            total: activities.length,
            byType: {},
            byStatus: {},
            bySymbol: {},
            successful: 0,
            failed: 0,
            pending: 0
        };
        
        activities.forEach(activity => {
            // By type
            summary.byType[activity.type] = (summary.byType[activity.type] || 0) + 1;
            
            // By status
            summary.byStatus[activity.status] = (summary.byStatus[activity.status] || 0) + 1;
            
            // By symbol
            if (activity.symbol) {
                summary.bySymbol[activity.symbol] = (summary.bySymbol[activity.symbol] || 0) + 1;
            }
            
            // Status counts
            switch (activity.status) {
                case 'SUCCESS':
                    summary.successful++;
                    break;
                case 'FAILED':
                    summary.failed++;
                    break;
                case 'PENDING':
                    summary.pending++;
                    break;
            }
        });
        
        return summary;
    }

    /**
     * Export as JSON
     */
    exportJson(data) {
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `activity-logs-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);
    }

    /**
     * Export as CSV
     */
    exportCsv(data) {
        const headers = ['Timestamp', 'Type', 'Symbol', 'Action', 'Quantity', 'Price', 'Status', 'Message'];
        const rows = data.activities.map(activity => [
            activity.timestamp,
            activity.type,
            activity.symbol || '',
            activity.action || '',
            activity.quantity || '',
            activity.price || '',
            activity.status,
            activity.message || ''
        ]);
        
        const csvContent = [headers, ...rows]
            .map(row => row.map(cell => `"${cell}"`).join(','))
            .join('\n');
        
        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `activity-logs-${new Date().toISOString().split('T')[0]}.csv`;
        a.click();
        URL.revokeObjectURL(url);
    }

    /**
     * Export as text
     */
    exportText(data) {
        let content = `ACTIVITY LOGS EXPORT\n`;
        content += `==================\n\n`;
        content += `Export Date: ${new Date().toLocaleString()}\n`;
        content += `Total Activities: ${data.activities.length}\n\n`;
        
        if (data.summary) {
            content += `SUMMARY STATISTICS\n`;
            content += `------------------\n`;
            content += `Total: ${data.summary.total}\n`;
            content += `Successful: ${data.summary.successful}\n`;
            content += `Failed: ${data.summary.failed}\n`;
            content += `Pending: ${data.summary.pending}\n\n`;
            
            content += `BY TYPE\n`;
            content += `-------\n`;
            Object.entries(data.summary.byType).forEach(([type, count]) => {
                content += `${type}: ${count}\n`;
            });
            content += '\n';
            
            content += `BY STATUS\n`;
            content += `---------\n`;
            Object.entries(data.summary.byStatus).forEach(([status, count]) => {
                content += `${status}: ${count}\n`;
            });
            content += '\n';
        }
        
        content += `ACTIVITY DETAILS\n`;
        content += `----------------\n`;
        
        data.activities.forEach(activity => {
            content += `\n${new Date(activity.timestamp).toLocaleString()}\n`;
            content += `Type: ${activity.type}\n`;
            content += `Symbol: ${activity.symbol || 'N/A'}\n`;
            content += `Status: ${activity.status}\n`;
            content += `Message: ${activity.message || 'No message'}\n`;
            if (activity.quantity) content += `Quantity: ${activity.quantity}\n`;
            if (activity.price) content += `Price: $${activity.price}\n`;
            content += '----------------------------------------\n`;
        });
        
        const blob = new Blob([content], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `activity-logs-${new Date().toISOString().split('T')[0]}.txt`;
        a.click();
        URL.revokeObjectURL(url);
    }

    /**
     * Utility functions
     */
    formatDate(dateString) {
        const date = new Date(dateString);
        const today = new Date();
        const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
        
        if (date.toDateString() === today.toDateString()) {
            return 'Today';
        } else if (date.toDateString() === yesterday.toDateString()) {
            return 'Yesterday';
        } else {
            return date.toLocaleDateString('en-US', { 
                weekday: 'short', 
                month: 'short', 
                day: 'numeric' 
            });
        }
    }

    formatTime(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleTimeString('en-US', { 
            hour: '2-digit', 
            minute: '2-digit' 
        });
    }

    updateLogCount() {
        const countElement = document.getElementById('logCount');
        countElement.textContent = `Showing ${this.filteredActivities.length} of ${this.activities.length} activities`;
    }

    updateLastUpdateTime() {
        const updateElement = document.getElementById('lastUpdate');
        updateElement.textContent = `Last updated: ${new Date().toLocaleTimeString()}`;
    }

    showLoadingIndicator() {
        document.getElementById('loadingIndicator').style.display = 'block';
    }

    hideLoadingIndicator() {
        document.getElementById('loadingIndicator').style.display = 'none';
    }

    showNoActivities() {
        document.getElementById('noActivities').style.display = 'block';
    }

    hideNoActivities() {
        document.getElementById('noActivities').style.display = 'none';
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <span>${message}</span>
            <button class="close" onclick="this.parentElement.remove()">&times;</button>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }
}

// Initialize activity logger
let activityLogger;

document.addEventListener('DOMContentLoaded', function() {
    activityLogger = new ActivityLogger();
});

// Global functions for HTML event handlers
function filterLogs() {
    activityLogger.filterLogs();
}

function clearFilters() {
    activityLogger.clearFilters();
}

function refreshLogs() {
    activityLogger.refreshLogs();
}

function exportLogs() {
    activityLogger.exportLogs();
}

function closeActivityModal() {
    document.getElementById('activityModal').style.display = 'none';
}

function closeExportModal() {
    document.getElementById('exportModal').style.display = 'none';
}

function exportActivity() {
    // Export individual activity (would be implemented based on requirements)
    activityLogger.showNotification('Individual activity export coming soon', 'info');
}

// Close modals when clicking outside
window.onclick = function(event) {
    const activityModal = document.getElementById('activityModal');
    const exportModal = document.getElementById('exportModal');
    
    if (event.target === activityModal) {
        activityModal.style.display = 'none';
    }
    if (event.target === exportModal) {
        exportModal.style.display = 'none';
    }
}

// Perform export function
function performExport() {
    activityLogger.performExport();
}