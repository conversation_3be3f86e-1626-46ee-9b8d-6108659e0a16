# 🚀 TradingView → Binance US Automated Trading Bot

**PRODUCTION READY & DEPLOYED** - Automated trading system using <PERSON><PERSON>'s Math Lines strategy.

## 🎉 **LIVE SYSTEM STATUS**

- ✅ **OPERATIONAL**: Railway deployment running 24/7
- ✅ **WEBHOOK URL**: `https://web-production-0efa7.up.railway.app/webhook`
- ✅ **AUTHENTICATION**: Webhook secret `itsMike818!` verified
- ✅ **TRADING PAIRS**: BTCUSD, BTCUSDT supported
- ✅ **RISK MANAGEMENT**: Uses 50% of available balance per trade
- ✅ **BINANCE US**: API connected and operational

## 🎯 **Quick Start - TradingView Setup**

### 1. Pine Script Configuration
Use the Pine Script from `pinescript.md` - it's already configured for production.

### 2. TradingView Alert Setup
1. **Condition**: Murrey's Math Lines Channel Strategy
2. **Webhook URL**: `https://web-production-0efa7.up.railway.app/webhook`
3. **Message**: `{{strategy.order.alert_message}}`
4. **Headers**: `X-Webhook-Secret: itsMike818!`

### 3. Start Trading
Your system is ready! Alerts will automatically execute trades on Binance US.

- 🚀 **24/7 Automated Trading**: Executes trades based on Murrey's Math signals
- 📈 **Binance US Integration**: Direct API connection for real-time trading
- 🔒 **Secure Authentication**: Webhook secret validation
- 📊 **Smart Risk Management**: Uses 50% of available balance per trade
- 📝 **Complete Logging**: All trades logged for monitoring
- 🌐 **Railway Hosting**: Free, reliable cloud deployment
- ⚡ **Real-time Execution**: Immediate response to TradingView alerts

## 📊 **Trading Strategy**

**Murrey's Math Lines Channel Strategy** with EMA confirmation:
- **Buy Signal**: Price crosses above lower Murrey line + EMA below midline
- **Sell Signal**: Price crosses below upper Murrey line + EMA above midline
- **Risk Management**: 50% of available balance per trade
- **Supported Pairs**: BTCUSD, BTCUSDT

## 🔗 **API Endpoints**

### Health Check
```
GET https://web-production-0efa7.up.railway.app/
```

### Webhook (TradingView Alerts)
```
POST https://web-production-0efa7.up.railway.app/webhook
Headers: X-Webhook-Secret: itsMike818!
Body: {"coin": "BTC", "action": "BUY", "market_order": "1"}
```

### Configuration Check
```
GET https://web-production-0efa7.up.railway.app/config
```

## ⚠️ **Important Notes**

### Safety Features
- Uses 50% of available balance per trade (configurable)
- Minimum order quantities to protect your account
- Emergency stop functionality available
- Comprehensive logging for all trades

### Security
- Webhook secret validation required
- Binance API keys stored securely in Railway environment
- No withdrawal permissions needed on API keys

### Monitoring
- Check Railway dashboard for logs: <https://railway.app/dashboard>
- Monitor Binance US account for trade executions
- All trades logged with timestamps and details

## 📄 **Files Overview**

- `app.py` - Main Flask webhook server
- `pinescript.md` - Production-ready Pine Script strategy
- `requirements.txt` - Python dependencies
- `nixpacks.toml` - Railway deployment configuration
- `Procfile` - Railway process configuration
- `DEPLOYMENT_GUIDE.md` - Detailed deployment instructions

## 🎉 **System Ready**

Your automated trading system is **LIVE and OPERATIONAL**!

- **Webhook URL**: `https://web-production-0efa7.up.railway.app/webhook`
- **Status**: All systems verified and working
- **Next Step**: Set up TradingView alerts using the Pine Script

**Happy Trading!** 🚀
