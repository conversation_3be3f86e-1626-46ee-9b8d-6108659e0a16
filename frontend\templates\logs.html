{% extends "base.html" %}

{% block title %}Activity Logs - TradingView Bot{% endblock %}

{% block content %}
<div class="container">
    <div class="page-header">
        <h1>Activity Logs</h1>
        <p class="subtitle">Monitor trading activity and system events</p>
    </div>

    <!-- Log Summary Cards -->
    <div class="log-summary-grid">
        <div class="card">
            <div class="card-body">
                <div class="log-summary-item">
                    <div class="summary-icon trades">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <div class="summary-content">
                        <h3>{{ total_trades or 0 }}</h3>
                        <p>Total Trades</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-body">
                <div class="log-summary-item">
                    <div class="summary-icon successful">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="summary-content">
                        <h3>{{ winning_trades or 0 }}</h3>
                        <p>Successful</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-body">
                <div class="log-summary-item">
                    <div class="summary-icon failed">
                        <i class="fas fa-times-circle"></i>
                    </div>
                    <div class="summary-content">
                        <h3>{{ losing_trades or 0 }}</h3>
                        <p>Failed</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-body">
                <div class="log-summary-item">
                    <div class="summary-icon win-rate">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div class="summary-content">
                        <h3>{{ win_rate or 0 }}%</h3>
                        <p>Win Rate</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Activity Filters -->
    <div class="card">
        <div class="card-header">
            <h3>Activity Filters</h3>
        </div>
        <div class="card-body">
            <div class="filter-grid">
                <div class="filter-item">
                    <label class="form-label">Activity Type</label>
                    <select id="activityType" class="form-select" onchange="filterLogs()">
                        <option value="">All Activities</option>
                        <option value="BUY">Buy Orders</option>
                        <option value="SELL">Sell Orders</option>
                        <option value="SYSTEM">System Events</option>
                        <option value="ALERT">Alerts</option>
                        <option value="TRADE">Trade Executions</option>
                    </select>
                </div>
                
                <div class="filter-item">
                    <label class="form-label">Symbol</label>
                    <select id="activitySymbol" class="form-select" onchange="filterLogs()">
                        <option value="">All Symbols</option>
                        {% for symbol in config.allowed_symbols %}
                        <option value="{{ symbol }}">{{ symbol }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="filter-item">
                    <label class="form-label">Status</label>
                    <select id="activityStatus" class="form-select" onchange="filterLogs()">
                        <option value="">All Statuses</option>
                        <option value="SUCCESS">Success</option>
                        <option value="PENDING">Pending</option>
                        <option value="FAILED">Failed</option>
                    </select>
                </div>
                
                <div class="filter-item">
                    <label class="form-label">Time Range</label>
                    <select id="timeRange" class="form-select" onchange="filterLogs()">
                        <option value="1">Last Hour</option>
                        <option value="6">Last 6 Hours</option>
                        <option value="24" selected>Last 24 Hours</option>
                        <option value="168">Last Week</option>
                        <option value="720">Last Month</option>
                    </select>
                </div>
            </div>
            
            <div class="filter-actions">
                <button class="btn btn-outline" onclick="clearFilters()">Clear Filters</button>
                <button class="btn btn-primary" onclick="exportLogs()">
                    <i class="fas fa-download"></i> Export Logs
                </button>
                <button class="btn btn-outline" onclick="refreshLogs()">
                    <i class="fas fa-sync"></i> Refresh
                </button>
            </div>
        </div>
    </div>

    <!-- Activity Timeline -->
    <div class="card">
        <div class="card-header">
            <h3>Activity Timeline</h3>
            <div class="log-header-stats">
                <span id="logCount">Loading activities...</span>
                <span id="lastUpdate">Last updated: Never</span>
            </div>
        </div>
        <div class="card-body">
            <div id="activityTimeline" class="activity-timeline">
                <!-- Activities will be loaded here -->
            </div>
            
            <!-- Loading indicator -->
            <div id="loadingIndicator" class="loading-indicator" style="display: none;">
                <i class="fas fa-spinner fa-spin"></i>
                <span>Loading activities...</span>
            </div>
            
            <!-- No activities message -->
            <div id="noActivities" class="no-activities" style="display: none;">
                <i class="fas fa-inbox"></i>
                <p>No activities found matching your criteria</p>
            </div>
        </div>
    </div>

    <!-- Real-time Updates -->
    <div class="card">
        <div class="card-header">
            <h3>Real-time Updates</h3>
            <div class="real-time-status">
                <span class="status-dot active"></span>
                <span>Live updates enabled</span>
            </div>
        </div>
        <div class="card-body">
            <div id="realTimeUpdates" class="real-time-updates">
                <div class="update-item system">
                    <div class="update-time">Just now</div>
                    <div class="update-content">
                        <i class="fas fa-plug"></i>
                        <span>System monitoring started</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Activity Detail Modal -->
<div id="activityModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Activity Details</h3>
            <button class="close" onclick="closeActivityModal()">&times;</button>
        </div>
        <div class="modal-body">
            <div id="activityDetails">
                <!-- Activity details will be loaded here -->
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-outline" onclick="closeActivityModal()">Close</button>
            <button class="btn btn-primary" onclick="exportActivity()">
                <i class="fas fa-download"></i> Export Activity
            </button>
        </div>
    </div>
</div>

<!-- Export Modal -->
<div id="exportModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Export Activity Logs</h3>
            <button class="close" onclick="closeExportModal()">&times;</button>
        </div>
        <div class="modal-body">
            <div class="export-options">
                <div class="export-option">
                    <label class="form-label">Export Format</label>
                    <select id="exportFormat" class="form-select">
                        <option value="json">JSON</option>
                        <option value="csv">CSV</option>
                        <option value="txt">Text</option>
                    </select>
                </div>
                
                <div class="export-option">
                    <label class="form-label">Date Range</label>
                    <input type="date" id="exportStartDate" class="form-input">
                    <span>to</span>
                    <input type="date" id="exportEndDate" class="form-input">
                </div>
                
                <div class="export-option">
                    <label class="form-label">Filter Options</label>
                    <div class="checkbox-group">
                        <label>
                            <input type="checkbox" id="exportIncludeDetails" checked>
                            Include detailed information
                        </label>
                        <label>
                            <input type="checkbox" id="exportIncludeStats" checked>
                            Include summary statistics
                        </label>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-outline" onclick="closeExportModal()">Cancel</button>
            <button class="btn btn-primary" onclick="performExport()">
                <i class="fas fa-download"></i> Export
            </button>
        </div>
    </div>
</div>

<!-- Status Legend -->
<div class="status-legend">
    <h4>Status Legend</h4>
    <div class="legend-items">
        <div class="legend-item">
            <span class="status-dot success"></span>
            <span>Success</span>
        </div>
        <div class="legend-item">
            <span class="status-dot pending"></span>
            <span>Pending</span>
        </div>
        <div class="legend-item">
            <span class="status-dot failed"></span>
            <span>Failed</span>
        </div>
        <div class="legend-item">
            <span class="status-dot info"></span>
            <span>Information</span>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="/static/js/logs.js"></script>
<script>
// Initialize logs page
document.addEventListener('DOMContentLoaded', function() {
    loadActivityLogs();
    startRealTimeUpdates();
    
    // Set default date range for export
    const today = new Date();
    const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    document.getElementById('exportStartDate').value = lastWeek.toISOString().split('T')[0];
    document.getElementById('exportEndDate').value = today.toISOString().split('T')[0];
    
    // Auto-refresh every 30 seconds
    setInterval(loadActivityLogs, 30000);
});

// Start real-time updates
function startRealTimeUpdates() {
    const updateContainer = document.getElementById('realTimeUpdates');
    
    // Simulate real-time updates
    setInterval(() => {
        const updateTypes = ['system', 'trade', 'alert'];
        const updateIcons = ['fas fa-info-circle', 'fas fa-exchange-alt', 'fas fa-bell'];
        const updateMessages = [
            'System health check completed',
            'Market data updated',
            'Trading strategy recalculated',
            'Account balance synchronized',
            'Configuration validated'
        ];
        
        const randomType = updateTypes[Math.floor(Math.random() * updateTypes.length)];
        const randomIcon = updateIcons[Math.floor(Math.random() * updateIcons.length)];
        const randomMessage = updateMessages[Math.floor(Math.random() * updateMessages.length)];
        
        const updateItem = document.createElement('div');
        updateItem.className = `update-item ${randomType}`;
        updateItem.innerHTML = `
            <div class="update-time">Just now</div>
            <div class="update-content">
                <i class="${randomIcon}"></i>
                <span>${randomMessage}</span>
            </div>
        `;
        
        updateContainer.insertBefore(updateItem, updateContainer.firstChild);
        
        // Keep only last 5 updates
        while (updateContainer.children.length > 5) {
            updateContainer.removeChild(updateContainer.lastChild);
        }
    }, 15000); // Update every 15 seconds
}
</script>
{% endblock %}