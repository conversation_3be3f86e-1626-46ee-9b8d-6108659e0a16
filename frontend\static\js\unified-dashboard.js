/**
 * Unified Dashboard - Single source of truth for dashboard functionality
 * This replaces all other conflicting dashboard initialization scripts
 */

class UnifiedDashboard {
    constructor() {
        this.api = window.api;
        this.isInitialized = false;
        this.updateInterval = null;
        this.lastUpdateTime = 0;
        
        this.init();
    }

    /**
     * Initialize dashboard
     */
    init() {
        console.log('Initializing Unified Dashboard...');
        
        // Wait for DOM and API to be ready
        this.waitForReady().then(() => {
            this.initializeDashboard();
            this.startAutoRefresh();
            this.isInitialized = true;
            console.log('Unified Dashboard initialized successfully');
        }).catch(error => {
            console.warn('Dashboard initialization failed:', error);
        });
    }

    /**
     * Wait for DOM and API to be ready
     */
    waitForReady() {
        return new Promise((resolve, reject) => {
            const checkReady = () => {
                if (document.readyState === 'complete' && window.api) {
                    resolve();
                } else {
                    setTimeout(checkReady, 100);
                }
            };
            
            // Timeout after 10 seconds
            setTimeout(() => {
                reject(new Error('Timeout waiting for DOM and API'));
            }, 10000);
            
            checkReady();
        });
    }

    /**
     * Initialize dashboard components
     */
    initializeDashboard() {
        this.setupTheme();
        this.setupNavigation();
        this.setupEventListeners();
        this.loadInitialData();
        this.setupErrorHandling();
    }

    /**
     * Setup theme
     */
    setupTheme() {
        const savedTheme = localStorage.getItem('theme') || 'light';
        document.documentElement.setAttribute('data-theme', savedTheme);
        
        this.updateThemeToggle(savedTheme);
    }

    /**
     * Update theme toggle button
     */
    updateThemeToggle(theme) {
        // Try multiple possible theme toggle element IDs
        const themeToggle = document.getElementById('themeToggle') || 
                           document.getElementById('theme-toggle') ||
                           document.querySelector('.theme-toggle');
        if (!themeToggle) return;
        
        // Try multiple possible icon selectors
        const lightIcon = themeToggle.querySelector('.fa-sun') || 
                         themeToggle.querySelector('.theme-light-icon') ||
                         themeToggle.querySelector('[class*="sun"]');
        const darkIcon = themeToggle.querySelector('.fa-moon') || 
                        themeToggle.querySelector('.theme-dark-icon') ||
                        themeToggle.querySelector('[class*="moon"]');
        
        if (lightIcon && darkIcon) {
            if (theme === 'dark') {
                lightIcon.style.display = 'none';
                darkIcon.style.display = 'inline-block';
            } else {
                lightIcon.style.display = 'inline-block';
                darkIcon.style.display = 'none';
            }
        }
    }

    /**
     * Setup navigation
     */
    setupNavigation() {
        const navLinks = document.querySelectorAll('.nav-link, .mobile-nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                
                const href = link.getAttribute('href');
                if (href && href !== '#') {
                    // Update active state
                    navLinks.forEach(l => l.classList.remove('active'));
                    link.classList.add('active');
                    
                    // Navigate if it's a frontend route
                    if (href.startsWith('/')) {
                        window.location.href = href;
                    }
                }
            });
        });
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Theme toggle
        const themeToggle = document.getElementById('themeToggle') || document.getElementById('theme-toggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => this.toggleTheme());
        }
        
        // Refresh button
        const refreshBtn = document.getElementById('refresh-dashboard');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.refreshDashboard());
        }
    }

    /**
     * Toggle theme
     */
    toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        
        document.documentElement.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);
        
        this.updateThemeToggle(newTheme);
        this.showToast(`Switched to ${newTheme} theme`, 'info');
    }

    /**
     * Setup error handling
     */
    setupErrorHandling() {
        // Global error handler
        window.addEventListener('error', (e) => {
            console.warn('Global error caught:', e.error);
            return false;
        });

        // Unhandled promise rejection handler
        window.addEventListener('unhandledrejection', (e) => {
            console.warn('Unhandled promise rejection:', e.reason);
            return false;
        });
    }

    /**
     * Load initial data
     */
    async loadInitialData() {
        try {
            this.showLoading();
            
            // Load data sequentially to avoid overwhelming the API
            await this.loadSystemStatus();
            await this.loadBalance();
            await this.loadConfiguration();
            await this.loadTradingStats();
            await this.loadRecentActivity();
            
            this.hideLoading();
            
        } catch (error) {
            console.warn('Initial data loading failed:', error);
            this.hideLoading();
            this.showToast('Some data failed to load. Please check your connection.', 'error');
        }
    }

    /**
     * Load system status
     */
    async loadSystemStatus() {
        try {
            const response = await this.api.healthCheck();
            this.updateConnectionStatus(response.success);
        } catch (error) {
            console.warn('System status loading failed:', error);
            this.updateConnectionStatus(false);
        }
    }

    /**
     * Update connection status
     */
    updateConnectionStatus(connected) {
        // Try multiple possible connection status element selectors
        const selectors = [
            '#connection-status',
            '#connectionStatus',
            '.connection-indicator',
            '.connection-status'
        ];
        
        let found = false;
        selectors.forEach(selector => {
            const element = document.querySelector(selector);
            if (element) {
                found = true;
                
                // Try to find status dot and text within the element
                const statusDot = element.querySelector('.status-dot');
                const statusText = element.querySelector('.status-text');
                
                if (statusDot) {
                    statusDot.className = `status-dot ${connected ? 'active' : 'inactive'}`;
                }
                
                if (statusText) {
                    statusText.textContent = connected ? 'Connected' : 'Disconnected';
                }
                
                // Update the element's class for styling
                if (element.id === 'connection-status' || element.id === 'connectionStatus') {
                    element.className = connected ? 'connected' : 'disconnected';
                }
            }
        });
        
        // If no connection elements found, just log the status
        if (!found) {
            console.log(`Connection status: ${connected ? 'Connected' : 'Disconnected'}`);
        }
    }

    /**
     * Load balance
     */
    async loadBalance() {
        try {
            const response = await this.api.getBalance();
            if (response.success) {
                this.updateBalanceDisplay(response.data);
            }
        } catch (error) {
            console.warn('Balance loading failed:', error);
        }
    }

    /**
     * Update balance display
     */
    updateBalanceDisplay(data) {
        if (!data || !data.balances) return;
        
        const totalValue = data.balances.reduce((sum, asset) => {
            const value = parseFloat(asset.free);
            if (asset.asset === 'BTC') return sum + value * 50000; // Approximate BTC price
            if (asset.asset === 'ETH') return sum + value * 3000; // Approximate ETH price
            return sum + value; // USDT and USD are already in USD
        }, 0);
        
        const balanceElement = document.getElementById('account-balance') || 
                              document.querySelector('[data-balance]') ||
                              document.querySelector('.stat-card .stat-value');
        
        if (balanceElement) {
            balanceElement.textContent = `$${totalValue.toFixed(2)}`;
        }
    }

    /**
     * Load configuration
     */
    async loadConfiguration() {
        try {
            const response = await this.api.getConfiguration();
            if (response.success) {
                this.updateConfigDisplay(response.data);
            }
        } catch (error) {
            console.warn('Configuration loading failed:', error);
        }
    }

    /**
     * Update configuration display
     */
    updateConfigDisplay(config) {
        if (!config) return;
        
        // Update emergency stop status
        const emergencyStatus = document.querySelector('.status-indicator .status-text');
        const emergencyToggle = document.getElementById('emergencyToggleBtn');
        
        if (emergencyStatus) {
            emergencyStatus.textContent = config.emergency_stop ? 'EMERGENCY STOP ACTIVE' : 'System Active';
        }
        
        if (emergencyToggle) {
            emergencyToggle.textContent = config.emergency_stop ? 'Enable Trading' : 'Emergency Stop';
            emergencyToggle.className = `btn ${config.emergency_stop ? 'success' : 'danger'} large`;
        }
    }

    /**
     * Load trading statistics
     */
    async loadTradingStats() {
        try {
            const response = await this.api.getTradingStats();
            if (response.success) {
                this.updateStatsDisplay(response.data);
            }
        } catch (error) {
            console.warn('Stats loading failed:', error);
        }
    }

    /**
     * Update statistics display
     */
    updateStatsDisplay(stats) {
        if (!stats) return;
        
        const elements = {
            'total-trades': stats.totalTrades || 0,
            'winning-trades': stats.winningTrades || 0,
            'losing-trades': stats.losingTrades || 0,
            'win-rate': stats.winRate ? `${stats.winRate}%` : '0%'
        };
        
        Object.keys(elements).forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = elements[id];
            }
        });
        
        // Update P&L displays
        const todayPnlElement = document.getElementById('today-pnl');
        const totalPnlElement = document.getElementById('total-pnl');
        
        if (todayPnlElement && stats.todayPnl) {
            todayPnlElement.textContent = `$${stats.todayPnl}`;
            todayPnlElement.className = `stat-change ${parseFloat(stats.todayPnl) >= 0 ? 'positive' : 'negative'}`;
        }
        
        if (totalPnlElement && stats.totalPnl) {
            totalPnlElement.textContent = `$${stats.totalPnl}`;
            totalPnlElement.className = `stat-change ${parseFloat(stats.totalPnl) >= 0 ? 'positive' : 'negative'}`;
        }
    }

    /**
     * Load recent activity
     */
    async loadRecentActivity() {
        try {
            const response = await this.api.getRecentActivity(10);
            if (response.success) {
                this.updateActivityDisplay(response.data);
            }
        } catch (error) {
            console.warn('Activity loading failed:', error);
        }
    }

    /**
     * Update activity display
     */
    updateActivityDisplay(activities) {
        if (!activities || !activities.length) return;
        
        const activityList = document.getElementById('activity-list') || 
                            document.querySelector('.activity-feed') ||
                            document.querySelector('#recent-trades-tbody');
        
        if (!activityList) return;
        
        // Clear existing content
        activityList.innerHTML = '';
        
        // Add recent activities
        activities.slice(0, 5).forEach(activity => {
            const activityItem = this.createActivityItem(activity);
            if (activityItem) {
                activityList.appendChild(activityItem);
            }
        });
    }

    /**
     * Create activity item
     */
    createActivityItem(activity) {
        const item = document.createElement('div');
        item.className = `activity-item ${activity.type.toLowerCase()} ${activity.status.toLowerCase()}`;
        
        const icon = this.getActivityIcon(activity.type);
        const time = new Date(activity.timestamp).toLocaleTimeString();
        
        item.innerHTML = `
            <div class="activity-icon status-${activity.status.toLowerCase()}">
                <i class="${icon}"></i>
            </div>
            <div class="activity-content">
                <div class="activity-title">${activity.type} ${activity.symbol || ''}</div>
                <div class="activity-time">${time}</div>
            </div>
        `;
        
        return item;
    }

    /**
     * Get activity icon
     */
    getActivityIcon(type) {
        const icons = {
            'BUY': 'fas fa-arrow-up',
            'SELL': 'fas fa-arrow-down',
            'SYSTEM': 'fas fa-cog',
            'ALERT': 'fas fa-bell',
            'TRADE': 'fas fa-exchange-alt'
        };
        return icons[type] || 'fas fa-info-circle';
    }

    
    /**
     * Show loading indicator
     */
    showLoading() {
        const loadingOverlay = document.getElementById('loadingOverlay') ||
                             document.querySelector('.loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'flex';
        }
    }

    /**
     * Hide loading indicator
     */
    hideLoading() {
        const loadingOverlay = document.getElementById('loadingOverlay') ||
                             document.querySelector('.loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
        }
    }

    /**
     * Show toast notification
     */
    showToast(message, type = 'info') {
        const toastContainer = document.getElementById('toastContainer');
        if (!toastContainer) return;
        
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <span>${message}</span>
            <button class="close" onclick="this.parentElement.remove()">&times;</button>
        `;
        
        toastContainer.appendChild(toast);
        
        setTimeout(() => {
            if (toast.parentElement) {
                toast.remove();
            }
        }, 5000);
    }

    /**
     * Refresh dashboard
     */
    async refreshDashboard() {
        try {
            this.showLoading();
            await this.loadInitialData();
            this.showToast('Dashboard refreshed', 'success');
        } catch (error) {
            this.showToast('Refresh failed', 'error');
        }
    }

    /**
     * Start auto refresh
     */
    startAutoRefresh() {
        // Auto-refresh every 30 seconds
        this.updateInterval = setInterval(() => {
            this.loadSystemStatus();
            this.loadBalance();
            this.loadTradingStats();
            this.loadRecentActivity();
        }, 30000);
    }

    /**
     * Stop auto refresh
     */
    stopAutoRefresh() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
    }
}

// Initialize dashboard when DOM is ready
let dashboardInstance;

document.addEventListener('DOMContentLoaded', function() {
    dashboardInstance = new UnifiedDashboard();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UnifiedDashboard;
}