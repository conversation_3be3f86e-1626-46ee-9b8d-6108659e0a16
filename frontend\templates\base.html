<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TradingView Bot - {% block title %}{% endblock %}</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/static/images/logo.svg">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/static/css/main.css">
    
    <!-- Theme Toggle Script -->
    <script>
        // Check for saved theme preference or default to 'light'
        const currentTheme = localStorage.getItem('theme') || 'light';
        document.documentElement.setAttribute('data-theme', currentTheme);
        
        // Apply theme to Tailwind
        if (currentTheme === 'dark') {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark');
        }
    </script>
    
    {% block head %}{% endblock %}
</head>
<body class="bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors duration-300">
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center" style="display: none;">
        <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-xl">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
            <p class="text-sm">Loading...</p>
        </div>
    </div>

    <!-- Navigation Header -->
    <nav class="bg-white dark:bg-gray-800 shadow-lg border-b border-gray-200 dark:border-gray-700">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center h-16">
                <!-- Logo and Title -->
                <div class="flex items-center space-x-3">
                    <img src="/static/images/logo.svg" alt="TradingView Bot" class="w-8 h-8">
                    <h1 class="text-xl font-bold text-gray-900 dark:text-white">TradingView Bot</h1>
                </div>
                
                <!-- Navigation Links -->
                <div class="hidden md:flex items-center space-x-6">
                    <a href="/dashboard" class="nav-link active" data-page="dashboard">
                        <i class="fas fa-chart-line mr-2"></i>Dashboard
                    </a>
                    <a href="/trading" class="nav-link" data-page="trading">
                        <i class="fas fa-exchange-alt mr-2"></i>Trading
                    </a>
                    <a href="/config" class="nav-link" data-page="config">
                        <i class="fas fa-cog mr-2"></i>Config
                    </a>
                    <a href="/logs" class="nav-link" data-page="logs">
                        <i class="fas fa-list mr-2"></i>Logs
                    </a>
                </div>
                
                <!-- Right Side Controls -->
                <div class="flex items-center space-x-4">
                    <!-- Connection Status -->
                    <div class="connection-indicator" id="connectionStatus">
                        <span class="status-dot active"></span>
                        <span class="status-text">Connected</span>
                    </div>
                    
                    <!-- Theme Toggle -->
                    <button id="themeToggle" class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                        <i class="fas fa-sun theme-light-icon"></i>
                        <i class="fas fa-moon theme-dark-icon" style="display: none;"></i>
                    </button>
                    
                    <!-- Mobile Menu Toggle -->
                    <button id="mobileMenuToggle" class="md:hidden p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
            
            <!-- Mobile Navigation -->
            <div id="mobileMenu" class="md:hidden border-t border-gray-200 dark:border-gray-700">
                <div class="py-2">
                    <a href="/dashboard" class="mobile-nav-link block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700" data-page="dashboard">
                        <i class="fas fa-chart-line mr-2"></i>Dashboard
                    </a>
                    <a href="/trading" class="mobile-nav-link block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700" data-page="trading">
                        <i class="fas fa-exchange-alt mr-2"></i>Trading
                    </a>
                    <a href="/config" class="mobile-nav-link block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700" data-page="config">
                        <i class="fas fa-cog mr-2"></i>Config
                    </a>
                    <a href="/logs" class="mobile-nav-link block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700" data-page="logs">
                        <i class="fas fa-list mr-2"></i>Logs
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Toast Container -->
    <div id="toastContainer" class="fixed top-20 right-4 z-40 space-y-2"></div>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 mt-16">
        <div class="container mx-auto px-4 py-6">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="text-sm text-gray-600 dark:text-gray-400">
                    © 2024 TradingView Bot. Professional trading automation.
                </div>
                <div class="flex items-center space-x-4 mt-2 md:mt-0">
                    <a href="#" class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100">
                        <i class="fas fa-shield-alt mr-1"></i>Security
                    </a>
                    <a href="#" class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100">
                        <i class="fas fa-file-alt mr-1"></i>Docs
                    </a>
                    <a href="#" class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100">
                        <i class="fas fa-life-ring mr-1"></i>Support
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="/static/js/core-api.js"></script>
    <script src="/static/js/unified-dashboard.js"></script>
    
    {% block scripts %}{% endblock %}
</body>
</html>