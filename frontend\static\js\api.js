/**
 * API Communication Layer
 * Handles all communication with the backend TradingView bot API
 */

class TradingBotAPI {
    constructor() {
        this.baseURL = 'https://web-production-0efa7.up.railway.app';
        this.defaultHeaders = {
            'Content-Type': 'application/json',
        };
        this.requestTimeout = 10000; // 10 seconds
        this.retryAttempts = 3;
        this.retryDelay = 1000; // 1 second
    }

    /**
     * Generic HTTP request method with retry logic
     * @param {string} method - HTTP method (GET, POST, PUT, DELETE)
     * @param {string} endpoint - API endpoint
     * @param {Object} data - Request data
     * @param {Object} headers - Additional headers
     * @param {number} timeout - Request timeout
     * @returns {Promise<Response>}
     */
    async request(method, endpoint, data = null, headers = {}, timeout = this.requestTimeout) {
        const url = `${this.baseURL}${endpoint}`;
        const requestHeaders = { ...this.defaultHeaders, ...headers };
        
        const options = {
            method,
            headers: requestHeaders,
            timeout,
        };

        // Add body for non-GET requests
        if (data && method !== 'GET') {
            options.body = JSON.stringify(data);
        }

        let lastError;
        
        for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
            try {
                const response = await this.fetchWithTimeout(url, options, timeout);
                
                // Handle HTTP errors
                if (!response.ok) {
                    const errorData = await this.parseResponse(response);
                    throw new Error(errorData.error || errorData.message || `HTTP ${response.status}: ${response.statusText}`);
                }
                
                return response;
                
            } catch (error) {
                lastError = error;
                
                // Don't retry for certain errors
                if (this.shouldNotRetry(error)) {
                    break;
                }
                
                // Log retry attempt
                console.warn(`Request attempt ${attempt} failed: ${error.message}`);
                
                // Wait before retrying (exponential backoff)
                if (attempt < this.retryAttempts) {
                    await this.delay(this.retryDelay * attempt);
                }
            }
        }
        
        throw lastError;
    }

    /**
     * Fetch with timeout implementation
     * @param {string} url - Request URL
     * @param {Object} options - Fetch options
     * @param {number} timeout - Timeout in milliseconds
     * @returns {Promise<Response>}
     */
    async fetchWithTimeout(url, options, timeout) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);
        
        try {
            const response = await fetch(url, {
                ...options,
                signal: controller.signal,
            });
            clearTimeout(timeoutId);
            return response;
        } catch (error) {
            clearTimeout(timeoutId);
            if (error.name === 'AbortError') {
                throw new Error(`Request timeout after ${timeout || 'unknown'}ms`);
            }
            throw error;
        }
    }

    /**
     * Parse response and handle different content types
     * @param {Response} response - Fetch response
     * @returns {Promise<any>}
     */
    async parseResponse(response) {
        const contentType = response.headers.get('content-type');
        
        if (contentType && contentType.includes('application/json')) {
            return response.json();
        } else if (contentType && contentType.includes('text/')) {
            return response.text();
        } else {
            return response.blob();
        }
    }

    /**
     * Determine if request should not be retried
     * @param {Error} error - Error object
     * @returns {boolean}
     */
    shouldNotRetry(error) {
        // Don't retry client errors (4xx) except for 429 (Too Many Requests)
        if (error.message.includes('HTTP 4')) {
            return !error.message.includes('HTTP 429');
        }
        
        // Don't retry for network errors when there's no network
        if (error.message.includes('Failed to fetch') || error.message.includes('NetworkError')) {
            return true;
        }
        
        // Don't retry for abort errors
        if (error.message.includes('timeout') || error.message.includes('AbortError')) {
            return true;
        }
        
        return false;
    }

    /**
     * Delay function for retry logic
     * @param {number} ms - Delay in milliseconds
     * @returns {Promise<void>}
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * GET request
     * @param {string} endpoint - API endpoint
     * @param {Object} params - Query parameters
     * @returns {Promise<any>}
     */
    async get(endpoint, params = {}) {
        // Add query parameters
        const url = new URL(`${this.baseURL}${endpoint}`);
        Object.keys(params).forEach(key => {
            if (params[key] !== null && params[key] !== undefined) {
                url.searchParams.append(key, params[key]);
            }
        });

        const response = await this.fetchWithTimeout(url.toString(), {
            method: 'GET',
            headers: this.defaultHeaders,
            timeout: this.requestTimeout,
        });

        return this.parseResponse(response);
    }

    /**
     * POST request
     * @param {string} endpoint - API endpoint
     * @param {Object} data - Request data
     * @param {Object} headers - Additional headers
     * @returns {Promise<any>}
     */
    async post(endpoint, data = {}, headers = {}) {
        return this.request('POST', endpoint, data, headers);
    }

    /**
     * PUT request
     * @param {string} endpoint - API endpoint
     * @param {Object} data - Request data
     * @param {Object} headers - Additional headers
     * @returns {Promise<any>}
     */
    async put(endpoint, data = {}, headers = {}) {
        return this.request('PUT', endpoint, data, headers);
    }

    /**
     * DELETE request
     * @param {string} endpoint - API endpoint
     * @param {Object} headers - Additional headers
     * @returns {Promise<any>}
     */
    async delete(endpoint, headers = {}) {
        return this.request('DELETE', endpoint, null, headers);
    }

    /**
     * Health check endpoint
     * @returns {Promise<Object>}
     */
    async healthCheck() {
        try {
            const response = await this.get('/');
            return {
                success: true,
                data: response,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * Get account balance
     * @returns {Promise<Object>}
     */
    async getBalance() {
        try {
            const response = await this.get('/balance');
            return {
                success: true,
                data: response,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * Get trading configuration
     * @returns {Promise<Object>}
     */
    async getConfiguration() {
        try {
            const response = await this.get('/config');
            return {
                success: true,
                data: response,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * Toggle emergency stop
     * @returns {Promise<Object>}
     */
    async toggleEmergencyStop() {
        try {
            const response = await this.post('/emergency-stop');
            return {
                success: true,
                data: response,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * Execute test trade
     * @param {Object} tradeData - Trade data
     * @returns {Promise<Object>}
     */
    async executeTestTrade(tradeData = {}) {
        try {
            const response = await this.post('/test', tradeData);
            return {
                success: true,
                data: response,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * Get market price for symbol
     * @param {string} symbol - Trading symbol
     * @returns {Promise<Object>}
     */
    async getMarketPrice(symbol) {
        try {
            // This would typically be a dedicated endpoint, for now we'll return mock data
            return {
                success: true,
                data: {
                    symbol: symbol,
                    price: Math.random() * 1000 + 50000, // Mock price
                    timestamp: new Date().toISOString()
                },
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * Get trading statistics
     * @returns {Promise<Object>}
     */
    async getTradingStats() {
        try {
            // Mock trading statistics
            const stats = {
                totalTrades: Math.floor(Math.random() * 100) + 50,
                winningTrades: Math.floor(Math.random() * 60) + 20,
                losingTrades: Math.floor(Math.random() * 40) + 10,
                winRate: 0,
                totalPnl: (Math.random() * 1000 - 500).toFixed(2),
                todayPnl: (Math.random() * 200 - 100).toFixed(2),
                activeOrders: Math.floor(Math.random() * 5),
                lastTrade: new Date(Date.now() - Math.random() * 86400000).toISOString()
            };
            
            // Calculate win rate
            stats.winRate = stats.totalTrades > 0 ? 
                ((stats.winningTrades / stats.totalTrades) * 100).toFixed(1) : 0;
            
            return {
                success: true,
                data: stats,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * Get recent activity
     * @param {number} limit - Number of items to return
     * @returns {Promise<Object>}
     */
    async getRecentActivity(limit = 50) {
        try {
            // Mock recent activity data
            const activities = [];
            const actionTypes = ['BUY', 'SELL', 'SYSTEM', 'ALERT', 'TRADE'];
            const symbols = ['BTCUSDT', 'BTCUSD', 'ETHUSDT', 'ETHUSD'];
            
            for (let i = 0; i < limit; i++) {
                const type = actionTypes[Math.floor(Math.random() * actionTypes.length)];
                const symbol = symbols[Math.floor(Math.random() * symbols.length)];
                
                activities.push({
                    id: Date.now() - i * 60000,
                    type: type,
                    symbol: symbol,
                    action: type === 'BUY' || type === 'SELL' ? type : null,
                    quantity: type === 'BUY' || type === 'SELL' ? (Math.random() * 0.01 + 0.001).toFixed(6) : null,
                    price: type === 'BUY' || type === 'SELL' ? (Math.random() * 1000 + 50000).toFixed(2) : null,
                    status: ['SUCCESS', 'PENDING', 'FAILED'][Math.floor(Math.random() * 3)],
                    timestamp: new Date(Date.now() - i * 60000).toISOString(),
                    message: `${type} ${symbol} executed`
                });
            }
            
            return {
                success: true,
                data: activities,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * Validate webhook data format
     * @param {Object} data - Webhook data
     * @returns {Object} - Validation result
     */
    validateWebhookData(data) {
        const errors = [];
        const warnings = [];
        
        // Check required fields
        if (!data) {
            errors.push('No data provided');
            return { valid: false, errors, warnings };
        }
        
        // Check for action field (new format)
        if (!data.action) {
            errors.push('Missing action field');
        } else if (!['BUY', 'SELL'].includes(data.action.toUpperCase())) {
            errors.push('Invalid action. Must be BUY or SELL');
        }
        
        // Check for coin field (new format)
        if (!data.coin) {
            warnings.push('No coin field provided - using legacy format');
        }
        
        // Check for symbol field (legacy format)
        if (!data.symbol) {
            if (!data.coin) {
                errors.push('Missing both symbol and coin fields');
            }
        }
        
        // Check quantity if provided
        if (data.quantity) {
            const qty = parseFloat(data.quantity);
            if (isNaN(qty) || qty <= 0) {
                errors.push('Invalid quantity');
            }
        }
        
        return {
            valid: errors.length === 0,
            errors,
            warnings
        };
    }

    /**
     * Format webhook data for submission
     * @param {Object} data - Raw webhook data
     * @returns {Object} - Formatted data
     */
    formatWebhookData(data) {
        const formatted = { ...data };
        
        // Convert coin to symbol if needed (new format)
        if (data.coin && !data.symbol) {
            formatted.symbol = data.coin + 'USD';
        }
        
        // Ensure action is uppercase
        if (formatted.action) {
            formatted.action = formatted.action.toUpperCase();
        }
        
        // Ensure symbol is uppercase
        if (formatted.symbol) {
            formatted.symbol = formatted.symbol.toUpperCase();
        }
        
        return formatted;
    }

    /**
     * Calculate trading quantity based on percentage
     * @param {string} symbol - Trading symbol
     * @param {string} side - Trade side (BUY/SELL)
     * @param {number} percentage - Percentage of balance to use
     * @returns {Promise<Object>}
     */
    async calculateQuantity(symbol, side, percentage = 50) {
        try {
            const balanceResponse = await this.getBalance();
            if (!balanceResponse.success) {
                throw new Error(balanceResponse.error);
            }
            
            const balances = balanceResponse.data.balances;
            let balance = 0;
            
            if (side.toUpperCase() === 'BUY') {
                // For buy orders, use quote asset (USDT/USD)
                const quoteAsset = symbol.includes('USDT') ? 'USDT' : 'USD';
                const balanceData = balances.find(b => b.asset === quoteAsset);
                balance = balanceData ? parseFloat(balanceData.free) : 0;
            } else {
                // For sell orders, use base asset (BTC/ETH)
                const baseAsset = symbol.replace('USDT', '').replace('USD', '');
                const balanceData = balances.find(b => b.asset === baseAsset);
                balance = balanceData ? parseFloat(balanceData.free) : 0;
            }
            
            const usableBalance = balance * (percentage / 100);
            
            // Get current price
            const priceResponse = await this.getMarketPrice(symbol);
            if (!priceResponse.success) {
                throw new Error(priceResponse.error);
            }
            
            const currentPrice = parseFloat(priceResponse.data.price);
            const quantity = usableBalance / currentPrice;
            
            return {
                success: true,
                data: {
                    symbol,
                    side,
                    percentage,
                    availableBalance: balance,
                    usableBalance,
                    currentPrice,
                    calculatedQuantity: quantity.toFixed(8),
                    timestamp: new Date().toISOString()
                },
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }
}

// Create global API instance
window.api = new TradingBotAPI();

// Export for use in modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TradingBotAPI;
}