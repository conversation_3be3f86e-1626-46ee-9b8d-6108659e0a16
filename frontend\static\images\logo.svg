<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="logo-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#10b981;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="chart-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="16" cy="16" r="15" fill="url(#logo-gradient)" opacity="0.1"/>
  <circle cx="16" cy="16" r="15" fill="none" stroke="url(#logo-gradient)" stroke-width="2"/>
  
  <!-- Chart line -->
  <path d="M 8 20 L 12 12 L 16 16 L 20 8 L 24 14" 
        fill="none" 
        stroke="url(#chart-gradient)" 
        stroke-width="2" 
        stroke-linecap="round" 
        stroke-linejoin="round"/>
  
  <!-- Chart points -->
  <circle cx="8" cy="20" r="2" fill="#10b981"/>
  <circle cx="12" cy="12" r="2" fill="#10b981"/>
  <circle cx="16" cy="16" r="2" fill="#3b82f6"/>
  <circle cx="20" cy="8" r="2" fill="#3b82f6"/>
  <circle cx="24" cy="14" r="2" fill="#10b981"/>
  
  <!-- TradingView/Tick icon -->
  <path d="M 22 10 L 26 6 L 24 4 L 22 6 L 20 4 L 18 6" 
        fill="none" 
        stroke="#10b981" 
        stroke-width="1.5" 
        stroke-linecap="round" 
        stroke-linejoin="round"/>
</svg>