/**
 * Main Frontend Application
 * Core frontend functionality and utilities
 */

class TradingBotApp {
    constructor() {
        this.currentPage = 'dashboard';
        this.loading = false;
        this.theme = localStorage.getItem('theme') || 'light';
        this.connectionStatus = 'connecting';
        this.lastActivity = Date.now();
        this.updateInterval = null;
        
        this.init();
    }

    /**
     * Initialize the application
     */
    init() {
        console.log('Initializing Trading Bot App...');
        
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initializeApp());
        } else {
            this.initializeApp();
        }
    }

    /**
     * Initialize the application when DOM is ready
     */
    initializeApp() {
        // Set up theme
        this.setTheme(this.theme);
        
        // Set up event listeners
        this.setupEventListeners();
        
        // Initialize navigation
        this.setupNavigation();
        
        // Initialize tooltips and popovers
        this.setupTooltips();
        
        // Set up keyboard shortcuts
        this.setupKeyboardShortcuts();
        
        // Set up error handling
        this.setupErrorHandling();
        
        // Set up connection monitoring
        this.setupConnectionMonitoring();
        
        // Start auto-refresh
        this.startAutoRefresh();
        
        // Initialize performance monitoring
        this.setupPerformanceMonitoring();
        
        console.log('Trading Bot App initialized successfully');
    }

    /**
     * Set up theme management
     */
    setTheme(theme) {
        document.body.setAttribute('data-theme', theme);
        this.theme = theme;
        
        // Update theme toggle button
        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            const icon = themeToggle.querySelector('i');
            icon.className = theme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
        }
        
        // Save theme preference
        localStorage.setItem('theme', theme);
        
        // Update chart colors if charts exist
        this.updateChartTheme();
    }

    /**
     * Toggle between light and dark themes
     */
    toggleTheme() {
        const newTheme = this.theme === 'light' ? 'dark' : 'light';
        this.setTheme(newTheme);
        this.showToast(`Switched to ${newTheme} theme`, 'info');
    }

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Theme toggle
        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => this.toggleTheme());
        }
        
        // Refresh button
        const refreshBtn = document.getElementById('refresh-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.refreshData());
        }
        
        // Window focus/blur events
        window.addEventListener('focus', () => this.handleWindowFocus());
        window.addEventListener('blur', () => this.handleWindowBlur());
        
        // Page visibility events
        document.addEventListener('visibilitychange', () => this.handleVisibilityChange());
        
        // Before unload
        window.addEventListener('beforeunload', (e) => this.handleBeforeUnload(e));
        
        // Click outside for modals
        document.addEventListener('click', (e) => this.handleOutsideClick(e));
    }

    /**
     * Set up navigation
     */
    setupNavigation() {
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const page = link.dataset.page;
                this.navigateTo(page);
            });
        });
    }

    /**
     * Navigate to a page
     */
    navigateTo(page) {
        if (this.currentPage === page) return;
        
        // Update navigation state
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
            if (link.dataset.page === page) {
                link.classList.add('active');
            }
        });
        
        // Update current page
        this.currentPage = page;
        
        // Update URL without navigation
        window.history.pushState({ page }, page, `/${page}`);
        
        // Show toast notification
        this.showToast(`Navigated to ${page}`, 'info');
        
        // Page-specific initialization
        this.initializePage(page);
    }

    /**
     * Initialize page-specific functionality
     */
    initializePage(page) {
        switch (page) {
            case 'dashboard':
                this.initializeDashboard();
                break;
            case 'trading':
                this.initializeTrading();
                break;
            case 'config':
                this.initializeConfig();
                break;
            case 'logs':
                this.initializeLogs();
                break;
            case 'settings':
                this.initializeSettings();
                break;
        }
    }

    /**
     * Set up tooltips
     */
    setupTooltips() {
        // Simple tooltip implementation
        const tooltipElements = document.querySelectorAll('[data-tooltip]');
        tooltipElements.forEach(element => {
            element.addEventListener('mouseenter', (e) => this.showTooltip(e));
            element.addEventListener('mouseleave', () => this.hideTooltip());
        });
    }

    /**
     * Show tooltip
     */
    showTooltip(event) {
        const element = event.target;
        const tooltipText = element.dataset.tooltip;
        const tooltip = document.createElement('div');
        
        tooltip.className = 'tooltip';
        tooltip.textContent = tooltipText;
        tooltip.style.position = 'absolute';
        tooltip.style.bottom = '100%';
        tooltip.style.left = '50%';
        tooltip.style.transform = 'translateX(-50%)';
        tooltip.style.marginBottom = '8px';
        tooltip.style.padding = '4px 8px';
        tooltip.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
        tooltip.style.color = 'white';
        tooltip.style.borderRadius = '4px';
        tooltip.style.fontSize = '12px';
        tooltip.style.whiteSpace = 'nowrap';
        tooltip.style.zIndex = '1000';
        
        element.style.position = 'relative';
        element.appendChild(tooltip);
        
        // Position tooltip to stay within viewport
        const rect = tooltip.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        
        if (rect.left < 0) {
            tooltip.style.left = '0';
            tooltip.style.transform = 'translateX(0)';
        } else if (rect.right > viewportWidth) {
            tooltip.style.left = '100%';
            tooltip.style.transform = 'translateX(-100%)';
            tooltip.style.marginBottom = '8px';
        }
    }

    /**
     * Hide tooltip
     */
    hideTooltip() {
        const tooltips = document.querySelectorAll('.tooltip');
        tooltips.forEach(tooltip => tooltip.remove());
    }

    /**
     * Set up keyboard shortcuts
     */
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + K: Focus search
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                this.focusSearch();
            }
            
            // Ctrl/Cmd + R: Refresh data
            if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
                e.preventDefault();
                this.refreshData();
            }
            
            // Ctrl/Cmd + D: Dashboard
            if ((e.ctrlKey || e.metaKey) && e.key === 'd') {
                e.preventDefault();
                this.navigateTo('dashboard');
            }
            
            // Ctrl/Cmd + T: Trading
            if ((e.ctrlKey || e.metaKey) && e.key === 't') {
                e.preventDefault();
                this.navigateTo('trading');
            }
            
            // Ctrl/Cmd + C: Configuration
            if ((e.ctrlKey || e.metaKey) && e.key === 'c') {
                e.preventDefault();
                this.navigateTo('config');
            }
            
            // Ctrl/Cmd + L: Logs
            if ((e.ctrlKey || e.metaKey) && e.key === 'l') {
                e.preventDefault();
                this.navigateTo('logs');
            }
            
            // Ctrl/Cmd + ,: Settings
            if ((e.ctrlKey || e.metaKey) && e.key === ',') {
                e.preventDefault();
                this.navigateTo('settings');
            }
            
            // Ctrl/Cmd + U: Toggle theme
            if ((e.ctrlKey || e.metaKey) && e.key === 'u') {
                e.preventDefault();
                this.toggleTheme();
            }
            
            // Escape: Close modals/tooltips
            if (e.key === 'Escape') {
                this.closeModals();
                this.hideTooltip();
            }
        });
    }

    /**
     * Set up error handling
     */
    setupErrorHandling() {
        // Global error handler
        window.addEventListener('error', (e) => {
            console.error('Global error:', e.error);
            this.showToast('An error occurred. Please try again.', 'error');
        });
        
        // Unhandled promise rejection handler
        window.addEventListener('unhandledrejection', (e) => {
            console.error('Unhandled promise rejection:', e.reason);
            this.showToast('An error occurred. Please try again.', 'error');
        });
        
        // Network error handler
        window.addEventListener('offline', () => {
            this.showToast('Network connection lost', 'error');
            this.updateConnectionStatus(false);
        });
        
        window.addEventListener('online', () => {
            this.showToast('Network connection restored', 'success');
            this.updateConnectionStatus(true);
        });
    }

    /**
     * Set up connection monitoring
     */
    setupConnectionMonitoring() {
        // Monitor API connection
        setInterval(() => {
            this.checkConnection();
        }, 30000); // Check every 30 seconds
        
        // Initial check
        this.checkConnection();
    }

    /**
     * Check backend connection
     */
    async checkConnection() {
        try {
            const response = await api.healthCheck();
            if (response.success) {
                this.updateConnectionStatus(true);
                this.connectionStatus = 'connected';
            } else {
                this.updateConnectionStatus(false);
                this.connectionStatus = 'disconnected';
            }
        } catch (error) {
            this.updateConnectionStatus(false);
            this.connectionStatus = 'disconnected';
        }
    }

    /**
     * Update connection status in UI
     */
    updateConnectionStatus(isConnected) {
        const statusBadge = document.getElementById('connection-status');
        if (statusBadge) {
            const statusText = statusBadge.querySelector('.status-text');
            const statusIcon = statusBadge.querySelector('i');
            
            if (isConnected) {
                statusBadge.className = 'nav-badge status-online';
                statusText.textContent = 'Connected';
                statusIcon.className = 'fas fa-circle';
            } else {
                statusBadge.className = 'nav-badge status-offline';
                statusText.textContent = 'Disconnected';
                statusIcon.className = 'fas fa-exclamation-circle';
            }
        }
    }

    /**
     * Start auto-refresh
     */
    startAutoRefresh() {
        // Auto-refresh every 30 seconds
        this.updateInterval = setInterval(() => {
            if (document.visibilityState === 'visible') {
                this.refreshData();
            }
        }, 30000);
    }

    /**
     * Stop auto-refresh
     */
    stopAutoRefresh() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
    }

    /**
     * Refresh all data
     */
    async refreshData() {
        if (this.loading) return;
        
        this.showLoading();
        this.showToast('Refreshing data...', 'info');
        
        try {
            // Refresh current page data
            switch (this.currentPage) {
                case 'dashboard':
                    await this.loadDashboardData();
                    break;
                case 'trading':
                    await this.loadTradingData();
                    break;
                case 'config':
                    await this.loadConfigData();
                    break;
                case 'logs':
                    await this.loadLogsData();
                    break;
                case 'settings':
                    await this.loadSettingsData();
                    break;
            }
            
            this.showToast('Data refreshed successfully', 'success');
        } catch (error) {
            console.error('Error refreshing data:', error);
            this.showToast('Error refreshing data', 'error');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * Set up performance monitoring
     */
    setupPerformanceMonitoring() {
        // Performance monitoring
        if (window.performance && window.performance.mark) {
            // Mark performance points
            window.performance.mark('app-init-start');
            
            // Mark when app is initialized
            window.addEventListener('load', () => {
                window.performance.mark('app-init-end');
                window.performance.measure('app-init-duration', 'app-init-start', 'app-init-end');
                
                const duration = window.performance.getEntriesByName('app-init-duration')[0];
                console.log('App initialized in:', duration.duration.toFixed(2) + 'ms');
            });
        }
    }

    /**
     * Handle window focus
     */
    handleWindowFocus() {
        this.lastActivity = Date.now();
        
        // Refresh data when window comes into focus
        if (document.visibilityState === 'visible') {
            this.refreshData();
        }
    }

    /**
     * Handle window blur
     */
    handleWindowBlur() {
        // Pause auto-refresh when window is not focused
        if (document.visibilityState === 'hidden') {
            this.stopAutoRefresh();
        }
    }

    /**
     * Handle visibility change
     */
    handleVisibilityChange() {
        if (document.visibilityState === 'visible') {
            this.startAutoRefresh();
            this.refreshData();
        } else {
            this.stopAutoRefresh();
        }
    }

    /**
     * Handle before unload
     */
    handleBeforeUnload(event) {
        // Clear intervals and timeouts
        this.stopAutoRefresh();
        
        // Save any unsaved data
        if (this.hasUnsavedData()) {
            event.preventDefault();
            event.returnValue = '';
        }
    }

    /**
     * Handle outside click
     */
    handleOutsideClick(event) {
        // Close modals when clicking outside
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            if (!modal.contains(event.target) && !event.target.closest('[data-modal-target]')) {
                this.closeModal(modal);
            }
        });
    }

    /**
     * Check for unsaved data
     */
    hasUnsavedData() {
        // Implement logic to check for unsaved data
        return false;
    }

    /**
     * Close all modals
     */
    closeModals() {
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => this.closeModal(modal));
    }

    /**
     * Close specific modal
     */
    closeModal(modal) {
        if (modal) {
            modal.classList.remove('active');
            const overlay = modal.previousElementSibling;
            if (overlay && overlay.classList.contains('modal-overlay')) {
                overlay.remove();
            }
        }
    }

    /**
     * Show loading overlay
     */
    showLoading() {
        this.loading = true;
        const loadingOverlay = document.getElementById('loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.classList.add('active');
        }
    }

    /**
     * Hide loading overlay
     */
    hideLoading() {
        this.loading = false;
        const loadingOverlay = document.getElementById('loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.classList.remove('active');
        }
    }

    /**
     * Show toast notification
     */
    showToast(message, type = 'info', duration = 5000) {
        const toastContainer = document.getElementById('toast-container');
        if (!toastContainer) return;
        
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        
        const icon = type === 'success' ? 'check-circle' : 
                     type === 'error' ? 'exclamation-circle' : 
                     type === 'warning' ? 'exclamation-triangle' : 'info-circle';
        
        toast.innerHTML = `
            <i class="fas fa-${icon}"></i>
            <span>${message}</span>
        `;
        
        toastContainer.appendChild(toast);
        
        // Animate in
        setTimeout(() => {
            toast.style.opacity = '1';
            toast.style.transform = 'translateX(0)';
        }, 10);
        
        // Auto remove
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => toast.remove(), 300);
        }, duration);
    }

    /**
     * Update chart theme
     */
    updateChartTheme() {
        // Update chart colors based on theme
        if (typeof Chart !== 'undefined' && Chart.instances) {
            Object.values(Chart.instances).forEach(chart => {
                // Update chart colors based on current theme
                if (chart.options.scales) {
                    Object.keys(chart.options.scales).forEach(scale => {
                        const axis = chart.options.scales[scale];
                        if (axis.ticks) {
                            axis.ticks.color = this.theme === 'dark' ? '#cbd5e1' : '#64748b';
                        }
                        if (axis.grid) {
                            axis.grid.color = this.theme === 'dark' ? '#334155' : '#e2e8f0';
                        }
                    });
                }
                
                if (chart.options.plugins && chart.options.plugins.legend) {
                    chart.options.plugins.legend.labels.color = this.theme === 'dark' ? '#f8fafc' : '#1e293b';
                }
                
                chart.update();
            });
        }
    }

    /**
     * Focus search
     */
    focusSearch() {
        const searchInput = document.querySelector('[data-search-input]');
        if (searchInput) {
            searchInput.focus();
            searchInput.select();
        }
    }

    /**
     * Initialize dashboard
     */
    initializeDashboard() {
        console.log('Initializing dashboard...');
        // Dashboard-specific initialization
    }

    /**
     * Initialize trading
     */
    initializeTrading() {
        console.log('Initializing trading...');
        // Trading-specific initialization
    }

    /**
     * Initialize config
     */
    initializeConfig() {
        console.log('Initializing config...');
        // Config-specific initialization
    }

    /**
     * Initialize logs
     */
    initializeLogs() {
        console.log('Initializing logs...');
        // Logs-specific initialization
    }

    /**
     * Initialize settings
     */
    initializeSettings() {
        console.log('Initializing settings...');
        // Settings-specific initialization
    }

    /**
     * Load dashboard data
     */
    async loadDashboardData() {
        // Dashboard data loading
        console.log('Loading dashboard data...');
    }

    /**
     * Load trading data
     */
    async loadTradingData() {
        // Trading data loading
        console.log('Loading trading data...');
    }

    /**
     * Load config data
     */
    async loadConfigData() {
        // Config data loading
        console.log('Loading config data...');
    }

    /**
     * Load logs data
     */
    async loadLogsData() {
        // Logs data loading
        console.log('Loading logs data...');
    }

    /**
     * Load settings data
     */
    async loadSettingsData() {
        // Settings data loading
        console.log('Loading settings data...');
    }

    /**
     * Clean up
     */
    destroy() {
        this.stopAutoRefresh();
        
        // Remove event listeners
        document.removeEventListener('keydown', this.handleKeyDown);
        document.removeEventListener('click', this.handleOutsideClick);
        
        // Clear intervals
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
    }
}

// Initialize application when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.tradingBotApp = new TradingBotApp();
});

// Export for use in modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TradingBotApp;
}