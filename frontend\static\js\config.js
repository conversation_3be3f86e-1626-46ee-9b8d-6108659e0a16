/**
 * Configuration Management JavaScript
 * Handles configuration updates, system testing, and status monitoring
 */

class ConfigManager {
    constructor() {
        this.api = window.api;
        this.currentConfig = null;
        this.testResults = [];
    }

    /**
     * Load configuration from backend
     */
    async loadConfiguration() {
        try {
            const response = await this.api.getConfiguration();
            if (response.success) {
                this.currentConfig = response.data;
                this.updateUI();
                return true;
            }
            throw new Error(response.error);
        } catch (error) {
            this.showNotification('Failed to load configuration: ' + error.message, 'error');
            return false;
        }
    }

    /**
     * Update UI with current configuration
     */
    updateUI() {
        if (!this.currentConfig) return;

        // Update emergency stop status
        const emergencyStatus = document.querySelector('.status-indicator .status-text');
        const emergencyToggleBtn = document.getElementById('emergencyToggleBtn');
        
        if (emergencyStatus) {
            emergencyStatus.textContent = this.currentConfig.emergency_stop ? 'EMERGENCY STOP ACTIVE' : 'System Active';
        }
        
        if (emergencyToggleBtn) {
            emergencyToggleBtn.textContent = this.currentConfig.emergency_stop ? 'Enable Trading' : 'Emergency Stop';
            emergencyToggleBtn.className = `btn ${this.currentConfig.emergency_stop ? 'success' : 'danger'} large`;
        }

        // Update quantity percentage
        const quantityInput = document.getElementById('quantityPercentage');
        if (quantityInput && this.currentConfig.quantity_percentage) {
            quantityInput.value = this.currentConfig.quantity_percentage;
        }

        // Update symbol grid
        const symbolGrid = document.querySelector('.symbol-grid');
        if (symbolGrid && this.currentConfig.allowed_symbols) {
            symbolGrid.innerHTML = '';
            this.currentConfig.allowed_symbols.forEach(symbol => {
                const symbolItem = document.createElement('div');
                symbolItem.className = 'symbol-item';
                symbolItem.setAttribute('data-symbol', symbol);
                symbolItem.innerHTML = `
                    <span class="symbol-name">${symbol}</span>
                    <span class="symbol-status active">Active</span>
                `;
                symbolGrid.appendChild(symbolItem);
            });
        }

        // Update order configuration
        this.updateOrderConfig();
    }

    /**
     * Update order configuration display
     */
    updateOrderConfig() {
        const configs = ['BTCUSDT', 'ETHUSDT', 'BTCUSD', 'ETHUSD'];
        
        configs.forEach(symbol => {
            const item = document.querySelector(`.order-config-item h5:contains("${symbol}")`)?.closest('.order-config-item');
            if (item && this.currentConfig.fixed_quantities) {
                const minInput = item.querySelector('input[type="text"]:first-of-type');
                const maxInput = item.querySelector('input[type="text"]:last-of-type');
                
                if (minInput && this.currentConfig.fixed_quantities[symbol]) {
                    minInput.value = this.currentConfig.fixed_quantities[symbol];
                }
                
                if (maxInput && this.currentConfig.max_quantities) {
                    maxInput.value = this.currentConfig.max_quantities[symbol];
                }
            }
        });
    }

    /**
     * Toggle emergency stop
     */
    async toggleEmergencyStop() {
        try {
            const response = await this.api.toggleEmergencyStop();
            if (response.success) {
                this.currentConfig = response.data;
                this.updateUI();
                this.showNotification('Emergency stop status updated', 'success');
                
                // Show success modal
                this.showSuccessModal('Emergency stop status updated successfully');
            } else {
                throw new Error(response.error);
            }
        } catch (error) {
            this.showNotification('Failed to toggle emergency stop: ' + error.message, 'error');
        }
    }

    /**
     * Update quantity percentage
     */
    async updateQuantityPercentage() {
        const percentageInput = document.getElementById('quantityPercentage');
        const newPercentage = parseFloat(percentageInput.value);
        
        if (isNaN(newPercentage) || newPercentage < 1 || newPercentage > 100) {
            this.showNotification('Please enter a valid percentage (1-100)', 'error');
            return;
        }

        try {
            // This would typically be a backend endpoint to update configuration
            // For now, we'll show a modal for confirmation
            this.showConfigModal(
                'Quantity Percentage',
                this.currentConfig.quantity_percentage,
                newPercentage,
                'percentage'
            );
        } catch (error) {
            this.showNotification('Failed to update quantity percentage: ' + error.message, 'error');
        }
    }

    /**
     * Refresh symbols from backend
     */
    async refreshSymbols() {
        try {
            const response = await this.api.getConfiguration();
            if (response.success) {
                this.currentConfig = response.data;
                this.updateUI();
                this.showNotification('Symbols refreshed successfully', 'success');
            } else {
                throw new Error(response.error);
            }
        } catch (error) {
            this.showNotification('Failed to refresh symbols: ' + error.message, 'error');
        }
    }

    /**
     * Test API connection
     */
    async testApiConnection() {
        const results = [];
        
        try {
            const response = await this.api.healthCheck();
            results.push({
                test: 'API Connection',
                status: response.success ? 'PASS' : 'FAIL',
                message: response.success ? 'Successfully connected to API' : response.error,
                details: response.success ? response.data : null
            });
        } catch (error) {
            results.push({
                test: 'API Connection',
                status: 'FAIL',
                message: error.message,
                details: null
            });
        }

        this.showTestResults(results);
    }

    /**
     * Test balance retrieval
     */
    async testBalance() {
        const results = [];
        
        try {
            const response = await this.api.getBalance();
            results.push({
                test: 'Balance Retrieval',
                status: response.success ? 'PASS' : 'FAIL',
                message: response.success ? 'Successfully retrieved balance' : response.error,
                details: response.success ? {
                    total: response.data.total_value,
                    available: response.data.available_balance,
                    assets: Object.keys(response.data.balances || {}).length
                } : null
            });
        } catch (error) {
            results.push({
                test: 'Balance Retrieval',
                status: 'FAIL',
                message: error.message,
                details: null
            });
        }

        this.showTestResults(results);
    }

    /**
     * Test configuration loading
     */
    async testConfiguration() {
        const results = [];
        
        try {
            const response = await this.api.getConfiguration();
            if (response.success) {
                const config = response.data;
                const hasRequired = config.emergency_stop !== undefined && 
                                  config.quantity_percentage && 
                                  config.allowed_symbols;
                
                results.push({
                    test: 'Configuration Loading',
                    status: hasRequired ? 'PASS' : 'FAIL',
                    message: hasRequired ? 'Configuration loaded successfully' : 'Missing required configuration fields',
                    details: hasRequired ? {
                        emergency_stop: config.emergency_stop ? 'Active' : 'Inactive',
                        quantity_percentage: config.quantity_percentage + '%',
                        symbols_count: config.allowed_symbols.length
                    } : null
                });
            } else {
                throw new Error(response.error);
            }
        } catch (error) {
            results.push({
                test: 'Configuration Loading',
                status: 'FAIL',
                message: error.message,
                details: null
            });
        }

        this.showTestResults(results);
    }

    /**
     * Run comprehensive diagnostics
     */
    async runDiagnostics() {
        const results = [];
        
        // Test API connection
        try {
            const response = await this.api.healthCheck();
            results.push({
                test: 'API Connection',
                status: response.success ? 'PASS' : 'FAIL',
                message: response.success ? 'Successfully connected to API' : response.error
            });
        } catch (error) {
            results.push({
                test: 'API Connection',
                status: 'FAIL',
                message: error.message
            });
        }

        // Test balance retrieval
        try {
            const response = await this.api.getBalance();
            results.push({
                test: 'Balance Retrieval',
                status: response.success ? 'PASS' : 'FAIL',
                message: response.success ? 'Successfully retrieved balance' : response.error
            });
        } catch (error) {
            results.push({
                test: 'Balance Retrieval',
                status: 'FAIL',
                message: error.message
            });
        }

        // Test configuration loading
        try {
            const response = await this.api.getConfiguration();
            if (response.success) {
                const config = response.data;
                const hasRequired = config.emergency_stop !== undefined && 
                                  config.quantity_percentage && 
                                  config.allowed_symbols;
                
                results.push({
                    test: 'Configuration Loading',
                    status: hasRequired ? 'PASS' : 'FAIL',
                    message: hasRequired ? 'Configuration loaded successfully' : 'Missing required configuration fields'
                });
            } else {
                throw new Error(response.error);
            }
        } catch (error) {
            results.push({
                test: 'Configuration Loading',
                status: 'FAIL',
                message: error.message
            });
        }

        // Test trading statistics
        try {
            const response = await this.api.getTradingStats();
            results.push({
                test: 'Trading Statistics',
                status: response.success ? 'PASS' : 'FAIL',
                message: response.success ? 'Successfully retrieved trading statistics' : response.error
            });
        } catch (error) {
            results.push({
                test: 'Trading Statistics',
                status: 'FAIL',
                message: error.message
            });
        }

        this.showTestResults(results);
    }

    /**
     * Show test results modal
     */
    showTestResults(results) {
        const testResults = document.getElementById('testResults');
        testResults.innerHTML = '';

        const overallStatus = results.every(r => r.status === 'PASS') ? 'All tests passed' : 
                             results.some(r => r.status === 'FAIL') ? 'Some tests failed' : 'No tests completed';

        // Overall status
        const statusHeader = document.createElement('div');
        statusHeader.className = 'test-status';
        statusHeader.innerHTML = `
            <h4>Overall Status: ${overallStatus}</h4>
        `;
        testResults.appendChild(statusHeader);

        // Individual results
        results.forEach(result => {
            const resultItem = document.createElement('div');
            resultItem.className = `test-result ${result.status.toLowerCase()}`;
            resultItem.innerHTML = `
                <div class="test-result-header">
                    <span class="test-result-status ${result.status.toLowerCase()}">${result.status}</span>
                    <span class="test-result-name">${result.test}</span>
                </div>
                <p class="test-result-message">${result.message}</p>
                ${result.details ? `<pre class="test-result-details">${JSON.stringify(result.details, null, 2)}</pre>` : ''}
            `;
            testResults.appendChild(resultItem);
        });

        // Show modal
        document.getElementById('testModal').style.display = 'block';
    }

    /**
     * Show configuration update modal
     */
    showConfigModal(setting, currentValue, newValue, type) {
        document.getElementById('configSetting').textContent = setting;
        document.getElementById('configCurrentValue').textContent = currentValue;
        document.getElementById('configNewValue').value = newValue;
        document.getElementById('configModal').style.display = 'block';
    }

    /**
     * Update configuration
     */
    async updateConfiguration() {
        const newValue = document.getElementById('configNewValue').value;
        
        try {
            // This would typically make an API call to update the configuration
            // For now, we'll simulate the update
            const setting = document.getElementById('configSetting').textContent;
            
            if (setting === 'Quantity Percentage') {
                this.currentConfig.quantity_percentage = parseFloat(newValue);
                await this.updateQuantityPercentageBackend(newValue);
            }
            
            closeModal();
            this.showSuccessModal('Configuration updated successfully');
            this.updateUI();
        } catch (error) {
            this.showNotification('Failed to update configuration: ' + error.message, 'error');
        }
    }

    /**
     * Update quantity percentage on backend
     */
    async updateQuantityPercentageBackend(percentage) {
        // This would typically call the backend API to update configuration
        // For demonstration purposes, we'll simulate the call
        await new Promise(resolve => setTimeout(resolve, 1000));
    }

    /**
     * Show success modal
     */
    showSuccessModal(message) {
        document.getElementById('successText').textContent = message;
        document.getElementById('successModal').style.display = 'block';
    }

    /**
     * Show notification
     */
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <span>${message}</span>
            <button class="close" onclick="this.parentElement.remove()">&times;</button>
        `;
        
        document.body.appendChild(notification);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }

    /**
     * Check API status
     */
    async checkApiStatus() {
        try {
            const response = await this.api.healthCheck();
            const statusElement = document.querySelector('.connection-status .status-text');
            if (statusElement) {
                statusElement.textContent = response.success ? 'Connected' : 'Disconnected';
                statusElement.parentElement.className = `connection-status ${response.success ? 'connected' : 'disconnected'}`;
            }
        } catch (error) {
            const statusElement = document.querySelector('.connection-status .status-text');
            if (statusElement) {
                statusElement.textContent = 'Disconnected';
                statusElement.parentElement.className = 'connection-status disconnected';
            }
        }
    }
}

// Initialize configuration manager
let configManager;

document.addEventListener('DOMContentLoaded', function() {
    configManager = new ConfigManager();
});

// Global functions for HTML event handlers
function toggleEmergencyStop() {
    configManager.toggleEmergencyStop();
}

function refreshSymbols() {
    configManager.refreshSymbols();
}

function testApiConnection() {
    configManager.testApiConnection();
}

function testBalance() {
    configManager.testBalance();
}

function testConfiguration() {
    configManager.testConfiguration();
}

function runDiagnostics() {
    configManager.runDiagnostics();
}

function updateConfiguration() {
    configManager.updateConfiguration();
}

function closeModal() {
    document.getElementById('configModal').style.display = 'none';
}

function closeTestModal() {
    document.getElementById('testModal').style.display = 'none';
}

function closeSuccessModal() {
    document.getElementById('successModal').style.display = 'none';
}

// Close modals when clicking outside
window.onclick = function(event) {
    const configModal = document.getElementById('configModal');
    const testModal = document.getElementById('testModal');
    const successModal = document.getElementById('successModal');
    
    if (event.target === configModal) {
        configModal.style.display = 'none';
    }
    if (event.target === testModal) {
        testModal.style.display = 'none';
    }
    if (event.target === successModal) {
        successModal.style.display = 'none';
    }
}