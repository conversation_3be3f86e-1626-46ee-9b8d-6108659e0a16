/* ===== Dashboard-Specific Styles ===== */

/* ===== Dashboard Grid ===== */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

@media (max-width: 768px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

/* ===== Stat Cards ===== */
.stat-card {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.stat-card:hover::before {
    opacity: 1;
}

.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.stat-title {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    position: relative;
    overflow: hidden;
}

.stat-icon::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-card:hover .stat-icon::after {
    opacity: 1;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    line-height: 1;
}

.stat-change {
    font-size: 0.875rem;
    font-weight: 500;
}

/* ===== Status Classes ===== */
.status-online {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.status-warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.status-offline {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

.status-info {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
}

/* ===== Change Colors ===== */
.positive {
    color: #10b981;
}

.negative {
    color: #ef4444;
}

.neutral {
    color: var(--text-secondary);
}

/* ===== Activity Feed ===== */
.activity-feed {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    height: 400px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.activity-feed .stat-header {
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.activity-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-color);
    transition: all var(--transition-fast);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-item:hover {
    background-color: var(--bg-tertiary);
    margin: 0 -1.5rem;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    border-radius: 8px;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
}

.activity-icon::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.activity-item:hover .activity-icon::after {
    opacity: 1;
}

.activity-content {
    flex: 1;
    min-width: 0;
}

.activity-title {
    font-weight: 500;
    margin-bottom: 0.25rem;
    color: var(--text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.activity-time {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

/* ===== Performance Chart ===== */
.chart-container {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    height: 400px;
    display: flex;
    flex-direction: column;
}

.chart-container .stat-header {
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.chart-wrapper {
    flex: 1;
    position: relative;
    min-height: 0;
}

/* ===== Recent Trades Table ===== */
.recent-trades-container {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    margin-top: 1.5rem;
}

.recent-trades-container .stat-header {
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

/* ===== Enhanced Dashboard Header ===== */
.page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-xl) 0;
    position: relative;
}

.page-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--border-color), transparent);
}

.header-content {
    flex: 1;
}

.header-actions {
    display: flex;
    gap: var(--spacing-sm);
    flex-shrink: 0;
}

/* ===== Connection Status Animation ===== */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.nav-badge.status-online i {
    animation: pulse 2s infinite;
}

/* ===== Loading State Animations ===== */
.stat-card.loading {
    position: relative;
    overflow: hidden;
}

.stat-card.loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* ===== Interactive Elements ===== */
.stat-card {
    cursor: pointer;
}

.stat-card:hover {
    transform: translateY(-2px) scale(1.02);
}

/* ===== Responsive Enhancements ===== */
@media (max-width: 768px) {
    .stat-value {
        font-size: 1.5rem;
    }
    
    .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
    
    .activity-feed,
    .chart-container {
        height: 300px;
    }
    
    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-base);
    }
    
    .header-actions {
        width: 100%;
        justify-content: flex-end;
    }
}

@media (max-width: 480px) {
    .stat-value {
        font-size: 1.25rem;
    }
    
    .stat-change {
        font-size: 0.75rem;
    }
    
    .activity-feed,
    .chart-container {
        height: 250px;
    }
    
    .dashboard-grid {
        gap: 0.75rem;
    }
    
    .stat-card {
        padding: 1rem;
    }
}

/* ===== Dark Mode Enhancements ===== */
[data-theme="dark"] .stat-card::before {
    background: linear-gradient(90deg, #60a5fa, #34d399);
}

[data-theme="dark"] .activity-item:hover {
    background-color: var(--bg-secondary);
}

[data-theme="dark"] .chart-container canvas {
    background: transparent;
}

/* ===== Accessibility Enhancements ===== */
.stat-card:focus-within,
.activity-item:focus-within {
    outline: 2px solid var(--accent-primary);
    outline-offset: 2px;
}

/* ===== Print Optimization ===== */
@media print {
    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }
    
    .stat-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .activity-feed,
    .chart-container {
        height: auto;
        max-height: 300px;
    }
    
    .page-header::after {
        display: none;
    }
}

/* ===== Custom Scrollbar for Activity Feed ===== */
.activity-feed::-webkit-scrollbar {
    width: 6px;
}

.activity-feed::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: 3px;
}

.activity-feed::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

.activity-feed::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}

/* ===== Performance Optimizations ===== */
.will-change-transform {
    will-change: transform;
}

.will-change-opacity {
    will-change: opacity;
}

/* ===== High Contrast Mode Support ===== */
@media (prefers-contrast: high) {
    .stat-card {
        border-width: 2px;
    }
    
    .stat-icon {
        border-width: 2px;
    }
    
    .activity-icon {
        border-width: 2px;
    }
}

/* ===== Reduced Motion Support ===== */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .nav-badge.status-online i {
        animation: none;
    }
    
    .stat-card:hover {
        transform: none;
    }
}

/* ===== Print Styles for Dashboard ===== */
@media print {
    .page-header {
        border-bottom: 2px solid #000;
        margin-bottom: 1rem;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr 1fr;
        gap: 0.5rem;
    }
    
    .stat-card {
        break-inside: avoid;
        page-break-inside: avoid;
    }
    
    .activity-feed {
        height: auto;
        max-height: 200px;
        overflow: visible;
    }
}