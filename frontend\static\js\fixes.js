/**
 * Temporary fixes for JavaScript issues
 */

// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, applying fixes...');
    
    // Fix theme toggle icon
    fixThemeToggleIcon();
    
    // Fix loading overlay
    fixLoadingOverlay();
    
    // Fix connection status
    fixConnectionStatus();
    
    // Initialize charts safely
    initChartsSafely();
});

/**
 * Fix theme toggle icon
 */
function fixThemeToggleIcon() {
    // Try both possible IDs
    const themeToggle = document.getElementById('themeToggle') || document.getElementById('theme-toggle');
    if (!themeToggle) return;
    
    const lightIcon = themeToggle.querySelector('.theme-light-icon') || themeToggle.querySelector('.fa-sun');
    const darkIcon = themeToggle.querySelector('.theme-dark-icon') || themeToggle.querySelector('.fa-moon');
    
    if (lightIcon && darkIcon) {
        const currentTheme = localStorage.getItem('theme') || 'light';
        if (currentTheme === 'dark') {
            lightIcon.style.display = 'none';
            darkIcon.style.display = 'inline';
        } else {
            lightIcon.style.display = 'inline';
            darkIcon.style.display = 'none';
        }
    }
}

/**
 * Fix loading overlay
 */
function fixLoadingOverlay() {
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.style.display = 'none';
        // Also ensure all loading indicators are hidden
        const loadingIndicators = document.querySelectorAll('.loading-indicator');
        loadingIndicators.forEach(indicator => {
            if (indicator.style) {
                indicator.style.display = 'none';
            }
        });
    }
}

/**
 * Fix connection status
 */
function fixConnectionStatus() {
    const statusElements = document.querySelectorAll('.connection-indicator');
    statusElements.forEach(element => {
        const statusDot = element.querySelector('.status-dot');
        const statusText = element.querySelector('.status-text');
        
        if (statusDot) {
            statusDot.className = 'status-dot active';
        }
        if (statusText) {
            statusText.textContent = 'Connected';
        }
    });
}

/**
 * Initialize charts safely
 */
function initChartsSafely() {
    // Check if Chart.js is loaded
    if (typeof Chart === 'undefined') {
        console.warn('Chart.js not loaded, skipping chart initialization');
        return;
    }
    
    // Initialize charts when their respective pages load
    setTimeout(() => {
        if (document.getElementById('performance-chart')) {
            console.log('Performance chart found, would initialize here');
        }
        if (document.getElementById('volume-chart')) {
            console.log('Volume chart found, would initialize here');
        }
        if (document.getElementById('winrate-chart')) {
            console.log('Win rate chart found, would initialize here');
        }
    }, 1000);
}

/**
 * Safe chart theme update
 */
function updateChartThemeSafely() {
    if (typeof Chart !== 'undefined' && Chart.instances) {
        Object.values(Chart.instances).forEach(chart => {
            try {
                if (chart.options.scales) {
                    Object.keys(chart.options.scales).forEach(scale => {
                        const axis = chart.options.scales[scale];
                        if (axis.ticks) {
                            axis.ticks.color = getComputedStyle(document.documentElement).getPropertyValue('--text-secondary');
                        }
                        if (axis.grid) {
                            axis.grid.color = getComputedStyle(document.documentElement).getPropertyValue('--border-color');
                        }
                    });
                }
                chart.update();
            } catch (error) {
                console.warn('Error updating chart theme:', error);
            }
        });
    }
}

/**
 * Safe API call with timeout
 */
function safeApiCall(endpoint, options = {}) {
    const api = window.api;
    if (!api) {
        console.warn('API not available');
        return Promise.reject(new Error('API not available'));
    }
    
    // Set reasonable timeout
    const timeout = options.timeout || 10000; // 10 seconds
    
    return new Promise((resolve, reject) => {
        const timeoutId = setTimeout(() => {
            reject(new Error(`Request timeout after ${timeout}ms`));
        }, timeout);
        
        api.get(endpoint)
            .then(response => {
                clearTimeout(timeoutId);
                resolve(response);
            })
            .catch(error => {
                clearTimeout(timeoutId);
                reject(error);
            });
    });
}

// Export functions for use in other scripts
window.fixes = {
    fixThemeToggleIcon,
    fixLoadingOverlay,
    fixConnectionStatus,
    initChartsSafely,
    updateChartThemeSafely,
    safeApiCall
};