from flask import Flask, render_template, send_from_directory, jsonify, request
import os
import requests

app_frontend = Flask(__name__, 
    static_url_path='/static',
    static_folder='frontend/static',
    template_folder='frontend/templates')

# Backend API configuration
BACKEND_URL = "http://localhost:8000"

@app_frontend.route('/')
def index():
    """Serve main dashboard"""
    return render_template('dashboard.html')

@app_frontend.route('/trading')
def trading():
    """Serve trading interface"""
    return render_template('trading.html')

@app_frontend.route('/config')
def config():
    """Serve configuration management"""
    try:
        # Fetch configuration from backend
        response = requests.get(f"{BACKEND_URL}/config")
        if response.status_code == 200:
            config_data = response.json()
            return render_template('config.html', config=config_data)
        else:
            # Fallback configuration
            fallback_config = {
                'quantity_percentage': 50,
                'allowed_symbols': ['BTCUSDT', 'BTCUSD'],
                'fixed_quantities': {'BTCUSDT': '0.001', 'BTCUSD': '0.001'},
                'max_quantities': {'BTCUSDT': '0.01', 'BTCUSD': '0.01'}
            }
            return render_template('config.html', config=fallback_config)
    except Exception as e:
        # Fallback configuration if backend is unavailable
        fallback_config = {
            'quantity_percentage': 50,
            'allowed_symbols': ['BTCUSDT', 'BTCUSD'],
            'fixed_quantities': {'BTCUSDT': '0.001', 'BTCUSD': '0.001'},
            'max_quantities': {'BTCUSDT': '0.01', 'BTCUSD': '0.01'}
        }
        return render_template('config.html', config=fallback_config)

@app_frontend.route('/logs')
def logs():
    """Serve activity logs"""
    return render_template('logs.html')

@app_frontend.route('/settings')
def settings():
    """Serve system settings"""
    return render_template('settings.html')

# API proxy endpoint to bypass CORS
@app_frontend.route('/api/', methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'])
@app_frontend.route('/api/<path:endpoint>', methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'])
def api_proxy(endpoint=''):
    """Proxy API requests to backend to avoid CORS issues"""
    try:
        # Handle empty endpoint (root path)
        if endpoint == '':
            backend_url = BACKEND_URL
        else:
            backend_url = f"{BACKEND_URL}/{endpoint}"
        
        # Get query parameters
        query_params = request.args.to_dict()
        
        # Make request to backend
        if request.method == 'GET':
            response = requests.get(backend_url, params=query_params, timeout=10)
        elif request.method == 'POST':
            response = requests.post(backend_url, json=request.get_json(), timeout=10)
        elif request.method == 'PUT':
            response = requests.put(backend_url, json=request.get_json(), timeout=10)
        elif request.method == 'DELETE':
            response = requests.delete(backend_url, timeout=10)
        else:
            return jsonify({'error': 'Method not allowed'}), 405
        
        # Return response with CORS headers
        return (response.text, response.status_code, {
            'Content-Type': response.headers.get('content-type', 'application/json'),
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Handle preflight requests
@app_frontend.route('/api/<path:endpoint>', methods=['OPTIONS'])
def api_options(endpoint):
    """Handle preflight CORS requests"""
    return '', 200, {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    }

# Serve static files
@app_frontend.route('/static/<path:path>')
def serve_static(path):
    return send_from_directory('frontend/static', path)

if __name__ == '__main__':
    port = int(os.environ.get('FRONTEND_PORT', 3000))
    app_frontend.run(host='0.0.0.0', port=port, debug=True)