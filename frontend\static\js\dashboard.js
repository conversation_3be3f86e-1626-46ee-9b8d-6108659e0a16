/**
 * Dashboard-specific functionality
 * Real-time dashboard with system health monitoring and trading metrics
 */

class DashboardManager {
    constructor() {
        this.chartInstances = {};
        this.updateInterval = null;
        this.lastUpdate = 0;
        this.initialized = false;
        this.performanceData = [];
        
        this.init();
    }

    /**
     * Initialize dashboard
     */
    init() {
        console.log('Initializing Dashboard Manager...');
        
        // Generate mock performance data for charts
        this.generateMockPerformanceData();
        
        // Set up dashboard-specific event listeners
        this.setupDashboardEventListeners();
        
        // Initialize performance chart
        this.initializePerformanceChart();
        
        // Initialize volume chart
        this.initializeVolumeChart();
        
        // Initialize win rate chart
        this.initializeWinRateChart();
        
        // Load initial data
        this.loadDashboardData();
        
        // Set up auto-refresh for dashboard
        this.startAutoRefresh();
        
        this.initialized = true;
        console.log('Dashboard Manager initialized successfully');
    }

    /**
     * Generate mock performance data for charts
     */
    generateMockPerformanceData() {
        const now = new Date();
        const dataPoints = 50;
        
        for (let i = dataPoints; i >= 0; i--) {
            const timestamp = new Date(now.getTime() - i * 60 * 60 * 1000); // Hourly data
            const basePrice = 50000;
            
            this.performanceData.push({
                timestamp: timestamp.toISOString(),
                pnl: (Math.random() * 2000 - 1000) + (i * 10),
                balance: basePrice + (Math.random() * 10000) + (i * 100),
                volume: (Math.random() * 100) + 50,
                winRate: Math.min(95, Math.max(40, 60 + (Math.random() * 20) - (i * 0.5)))
            });
        }
    }

    /**
     * Set up dashboard-specific event listeners
     */
    setupDashboardEventListeners() {
        // Refresh button
        const refreshBtn = document.getElementById('refresh-dashboard');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.refreshDashboardData());
        }
        
        // Chart period selector
        const chartPeriod = document.getElementById('chart-period');
        if (chartPeriod) {
            chartPeriod.addEventListener('change', (e) => this.updateChartPeriod(e.target.value));
        }
        
        // Emergency stop toggle
        const emergencyStopBtn = document.querySelector('[data-emergency-stop]');
        if (emergencyStopBtn) {
            emergencyStopBtn.addEventListener('click', () => this.toggleEmergencyStop());
        }
        
        // Set up stat card clicks
        this.setupStatCardInteractions();
    }

    /**
     * Set up stat card interactions
     */
    setupStatCardInteractions() {
        const statCards = document.querySelectorAll('.stat-card');
        statCards.forEach(card => {
            card.addEventListener('click', () => {
                const cardType = card.querySelector('.stat-title').textContent;
                this.handleStatCardClick(cardType, card);
            });
            
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-2px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0) scale(1)';
            });
        });
    }

    /**
     * Handle stat card clicks
     */
    handleStatCardClick(cardType, cardElement) {
        console.log(`Stat card clicked: ${cardType}`);
        
        switch (cardType) {
            case 'System Status':
                this.showSystemDetails();
                break;
            case 'Account Balance':
                this.showBalanceDetails();
                break;
            case 'Today\'s P&L':
                this.showPnLDetails();
                break;
            case 'Active Orders':
                this.navigateTo('trading');
                break;
            case 'Emergency Stop':
                this.toggleEmergencyStop();
                break;
            case 'Webhook Status':
                this.showWebhookDetails();
                break;
        }
    }

    /**
     * Show system details modal
     */
    showSystemDetails() {
        const modal = this.createModal('System Details', `
            <div class="system-details">
                <div class="detail-item">
                    <span class="detail-label">Backend URL:</span>
                    <span class="detail-value">https://web-production-0efa7.up.railway.app</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Connection Status:</span>
                    <span class="detail-value status-${this.connectionStatus}">${this.connectionStatus}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Last Update:</span>
                    <span class="detail-value">${new Date().toLocaleString()}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Response Time:</span>
                    <span class="detail-value">${this.lastResponseTime || 'N/A'}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">API Version:</span>
                    <span class="detail-value">v1.0.0</span>
                </div>
            </div>
        `);
        
        this.showModal(modal);
    }

    /**
     * Show balance details
     */
    showBalanceDetails() {
        api.getBalance().then(response => {
            if (response.success) {
                const balances = response.data.balances;
                let balanceHtml = '<div class="balance-details">';
                
                balances.forEach(balance => {
                    if (parseFloat(balance.free) > 0 || parseFloat(balance.locked) > 0) {
                        balanceHtml += `
                            <div class="balance-item">
                                <span class="balance-asset">${balance.asset}</span>
                                <span class="balance-free">Free: ${parseFloat(balance.free).toFixed(8)}</span>
                                <span class="balance-locked">Locked: ${parseFloat(balance.locked).toFixed(8)}</span>
                            </div>
                        `;
                    }
                });
                
                balanceHtml += '</div>';
                
                const modal = this.createModal('Account Balance Details', balanceHtml);
                this.showModal(modal);
            } else {
                this.showToast('Error loading balance details', 'error');
            }
        });
    }

    /**
     * Show P&L details
     */
    showPnLDetails() {
        const modal = this.createModal('Performance Details', `
            <div class="performance-details">
                <div class="performance-grid">
                    <div class="performance-stat">
                        <span class="stat-label">Total P&L</span>
                        <span class="stat-value">$${this.totalPnL || '0.00'}</span>
                    </div>
                    <div class="performance-stat">
                        <span class="stat-label">Win Rate</span>
                        <span class="stat-value">${this.winRate || '0'}%</span>
                    </div>
                    <div class="performance-stat">
                        <span class="stat-label">Total Trades</span>
                        <span class="stat-value">${this.totalTrades || '0'}</span>
                    </div>
                    <div class="performance-stat">
                        <span class="stat-label">Winning Trades</span>
                        <span class="stat-value positive">${this.winningTrades || '0'}</span>
                    </div>
                    <div class="performance-stat">
                        <span class="stat-label">Losing Trades</span>
                        <span class="stat-value negative">${this.losingTrades || '0'}</span>
                    </div>
                    <div class="performance-stat">
                        <span class="stat-label">Avg Trade Size</span>
                        <span class="stat-value">$${this.avgTradeSize || '0.00'}</span>
                    </div>
                </div>
                <div class="performance-chart">
                    <canvas id="performance-detail-chart"></canvas>
                </div>
            </div>
        `);
        
        this.showModal(modal);
        
        // Initialize performance detail chart after modal is shown
        setTimeout(() => {
            this.initializePerformanceDetailChart();
        }, 100);
    }

    /**
     * Show webhook details
     */
    showWebhookDetails() {
        const webhookUrl = 'https://web-production-0efa7.up.railway.app/webhook';
        const webhookSecret = 'itsMike818!';
        
        const modal = this.createModal('Webhook Configuration', `
            <div class="webhook-details">
                <div class="webhook-info">
                    <div class="webhook-item">
                        <span class="webhook-label">Webhook URL:</span>
                        <span class="webhook-value">${webhookUrl}</span>
                    </div>
                    <div class="webhook-item">
                        <span class="webhook-label">Secret Key:</span>
                        <span class="webhook-value">${webhookSecret}</span>
                    </div>
                    <div class="webhook-item">
                        <span class="webhook-label">Content Type:</span>
                        <span class="webhook-value">application/json</span>
                    </div>
                </div>
                <div class="webhook-examples">
                    <h4>Example Payloads:</h4>
                    <div class="example-tabs">
                        <button class="tab-btn active" data-tab="new-format">New Format (Pine Script)</button>
                        <button class="tab-btn" data-tab="legacy-format">Legacy Format</button>
                    </div>
                    <div class="tab-content active" id="new-format">
                        <pre><code>{
  "coin": "BTC",
  "action": "BUY",
  "market_order": "1"
}</code></pre>
                    </div>
                    <div class="tab-content" id="legacy-format">
                        <pre><code>{
  "action": "BUY",
  "symbol": "BTCUSD"
}</code></pre>
                    </div>
                </div>
                <div class="webhook-test">
                    <button class="btn btn-primary" onclick="dashboard.testWebhook()">
                        <i class="fas fa-plug"></i> Test Webhook
                    </button>
                </div>
            </div>
        `);
        
        this.showModal(modal);
        
        // Set up tab switching
        this.setupTabSwitching();
    }

    /**
     * Initialize performance chart
     */
    initializePerformanceChart() {
        const ctx = document.getElementById('performance-chart');
        if (!ctx) return;
        
        const chartCanvas = ctx.getContext('2d');
        
        // Generate sample data for the last 24 hours
        const labels = [];
        const data = [];
        const now = new Date();
        
        for (let i = 23; i >= 0; i--) {
            const time = new Date(now.getTime() - i * 60 * 60 * 1000);
            labels.push(time.getHours() + ':00');
            data.push(Math.random() * 1000 - 500); // Random P&L values
        }
        
        this.chartInstances.performance = new Chart(chartCanvas, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'P&L ($)',
                    data: data,
                    borderColor: '#10b981',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    tension: 0.4,
                    fill: true,
                    pointRadius: 3,
                    pointHoverRadius: 6,
                    pointBackgroundColor: '#10b981',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        borderColor: '#10b981',
                        borderWidth: 1,
                        cornerRadius: 8,
                        displayColors: false,
                        callbacks: {
                            label: function(context) {
                                return `P&L: $${context.parsed.y.toFixed(2)}`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)',
                            drawBorder: false
                        },
                        ticks: {
                            color: 'var(--text-secondary)',
                            font: {
                                size: 12
                            },
                            callback: function(value) {
                                return '$' + value;
                            }
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)',
                            drawBorder: false
                        },
                        ticks: {
                            color: 'var(--text-secondary)',
                            font: {
                                size: 12
                            },
                            maxRotation: 45,
                            minRotation: 0
                        }
                    }
                }
            }
        });
    }

    /**
     * Initialize volume chart
     */
    initializeVolumeChart() {
        const canvas = document.getElementById('volume-chart');
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        
        const labels = this.performanceData.map(item => 
            new Date(item.timestamp).toLocaleDateString('en-US', { 
                month: 'short', 
                day: 'numeric' 
            })
        );
        
        const volumeData = this.performanceData.map(item => item.volume);
        
        this.chartInstances.volume = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Trading Volume',
                    data: volumeData,
                    backgroundColor: 'rgba(16, 185, 129, 0.6)',
                    borderColor: '#10b981',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: getComputedStyle(document.documentElement).getPropertyValue('--text-secondary')
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: getComputedStyle(document.documentElement).getPropertyValue('--text-secondary')
                        }
                    }
                }
            }
        });
    }

    /**
     * Initialize win rate chart
     */
    initializeWinRateChart() {
        const canvas = document.getElementById('winrate-chart');
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        
        const labels = this.performanceData.map(item => 
            new Date(item.timestamp).toLocaleDateString('en-US', { 
                month: 'short', 
                day: 'numeric' 
            })
        );
        
        const winRateData = this.performanceData.map(item => item.winRate);
        
        this.chartInstances.winRate = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Win Rate (%)',
                    data: winRateData,
                    borderColor: '#f59e0b',
                    backgroundColor: 'rgba(245, 158, 11, 0.1)',
                    tension: 0.4,
                    fill: true,
                    pointRadius: 3,
                    pointHoverRadius: 5,
                    pointBackgroundColor: '#f59e0b',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        min: 0,
                        max: 100,
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: getComputedStyle(document.documentElement).getPropertyValue('--text-secondary'),
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: getComputedStyle(document.documentElement).getPropertyValue('--text-secondary')
                        }
                    }
                }
            }
        });
    }

    /**
     * Initialize performance detail chart
     */
    initializePerformanceDetailChart() {
        const modal = document.querySelector('[data-modal="Performance Details"]');
        if (!modal) return;
        
        const canvas = modal.querySelector('#performance-detail-chart');
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        
        // Generate more detailed performance data
        const labels = [];
        const data = [];
        const now = new Date();
        
        for (let i = 47; i >= 0; i--) {
            const time = new Date(now.getTime() - i * 30 * 60 * 1000); // 30-minute intervals
            labels.push(time.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }));
            data.push(Math.random() * 200 - 100);
        }
        
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'P&L ($)',
                    data: data,
                    borderColor: '#3b82f6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.4,
                    fill: true,
                    pointRadius: 2,
                    pointHoverRadius: 4,
                    pointBackgroundColor: '#3b82f6',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: 'var(--text-secondary)',
                            callback: function(value) {
                                return '$' + value;
                            }
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: 'var(--text-secondary)',
                            maxRotation: 45
                        }
                    }
                }
            }
        });
    }

    /**
     * Update chart period
     */
    updateChartPeriod(period) {
        console.log(`Updating chart period: ${period}`);
        
        // Update chart data based on period
        let labels, dataPoints;
        
        switch (period) {
            case '1d':
                labels = 24; // 24 hours
                dataPoints = 24;
                break;
            case '7d':
                labels = 7; // 7 days
                dataPoints = 7;
                break;
            case '30d':
                labels = 30; // 30 days
                dataPoints = 30;
                break;
        }
        
        // Generate new data based on period
        const labelsArray = [];
        const dataArray = [];
        const now = new Date();
        
        for (let i = labels - 1; i >= 0; i--) {
            if (period === '1d') {
                const time = new Date(now.getTime() - i * 60 * 60 * 1000);
                labelsArray.push(time.getHours() + ':00');
            } else {
                const time = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
                labelsArray.push(time.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }));
            }
            
            // Generate cumulative P&L data
            const baseValue = Math.random() * 1000 - 500;
            const cumulativeValue = i === 0 ? baseValue : baseValue + dataArray[dataArray.length - 1];
            dataArray.push(cumulativeValue);
        }
        
        // Update chart if it exists
        if (this.chartInstances.performance) {
            this.chartInstances.performance.data.labels = labelsArray;
            this.chartInstances.performance.data.datasets[0].data = dataArray;
            this.chartInstances.performance.update('active');
        }
        
        this.showToast(`Chart updated to show ${period}`, 'success');
    }

    /**
     * Load dashboard data
     */
    async loadDashboardData() {
        try {
            const startTime = Date.now();
            
            // Load all data in parallel
            const [systemStatus, balance, config, stats, activity] = await Promise.all([
                this.loadSystemStatus(),
                this.loadAccountBalance(),
                this.loadConfiguration(),
                this.loadTradingStats(),
                this.loadRecentActivity()
            ]);
            
            // Calculate response time
            this.lastResponseTime = `${Date.now() - startTime}ms`;
            
            // Update UI with loaded data
            this.updateSystemStatus(systemStatus);
            this.updateAccountBalance(balance);
            this.updateConfiguration(config);
            this.updateTradingStats(stats);
            this.updateRecentActivity(activity);
            
            // Update total P&L and other global variables
            if (stats.success && stats.data) {
                this.totalPnL = stats.data.totalPnL;
                this.winRate = stats.data.winRate;
                this.totalTrades = stats.data.totalTrades;
                this.winningTrades = stats.data.winningTrades;
                this.losingTrades = stats.data.losingTrades;
                this.avgTradeSize = stats.data.avgTradeSize;
            }
            
        } catch (error) {
            console.error('Error loading dashboard data:', error);
            this.showToast('Error loading dashboard data', 'error');
        }
    }

    /**
     * Load system status
     */
    async loadSystemStatus() {
        const response = await api.get('/');
        return response;
    }

    /**
     * Load account balance
     */
    async loadAccountBalance() {
        const response = await api.getBalance();
        return response;
    }

    /**
     * Load configuration
     */
    async loadConfiguration() {
        const response = await api.getConfiguration();
        return response;
    }

    /**
     * Load trading statistics
     */
    async loadTradingStats() {
        const response = await api.getTradingStats();
        return response;
    }

    /**
     * Load recent activity
     */
    async loadRecentActivity() {
        const response = await api.getRecentActivity(20);
        return response;
    }

    /**
     * Update system status in UI
     */
    updateSystemStatus(systemStatus) {
        if (!systemStatus.success) return;
        
        const statusElement = document.getElementById('system-status');
        const changeElement = document.getElementById('system-status-change');
        const iconElement = document.getElementById('system-status-icon');
        
        if (systemStatus.data.status === 'active') {
            statusElement.textContent = 'Online';
            statusElement.className = 'stat-value positive';
            changeElement.textContent = 'All systems operational';
            changeElement.className = 'stat-change positive';
            iconElement.className = 'stat-icon status-online';
        } else {
            statusElement.textContent = 'Offline';
            statusElement.className = 'stat-value negative';
            changeElement.textContent = 'System not responding';
            changeElement.className = 'stat-change negative';
            iconElement.className = 'stat-icon status-offline';
        }
        
        updateConnectionStatus(systemStatus.data.status === 'active');
    }

    /**
     * Update account balance in UI
     */
    updateAccountBalance(balance) {
        if (!balance.success) return;
        
        let totalBalance = 0;
        balance.data.balances.forEach(balanceItem => {
            totalBalance += parseFloat(balanceItem.free);
        });
        
        document.getElementById('account-balance').textContent = formatCurrency(totalBalance);
    }

    /**
     * Update configuration in UI
     */
    updateConfiguration(config) {
        if (!config.success) return;
        
        // Update emergency stop status
        const emergencyStopElement = document.getElementById('emergency-stop-status');
        const emergencyStopIcon = document.getElementById('emergency-stop-icon');
        
        if (config.data.emergency_stop) {
            emergencyStopElement.textContent = 'Stopped';
            emergencyStopElement.className = 'stat-value negative';
            emergencyStopIcon.className = 'stat-icon status-warning';
        } else {
            emergencyStopElement.textContent = 'Active';
            emergencyStopElement.className = 'stat-value positive';
            emergencyStopIcon.className = 'stat-icon status-online';
        }
    }

    /**
     * Update trading statistics in UI
     */
    updateTradingStats(stats) {
        if (!stats.success || !stats.data) return;
        
        // Today's P&L
        const todayPnlElement = document.getElementById('today-pnl');
        const todayPnlChangeElement = document.getElementById('today-pnl-change');
        
        if (stats.data.todayPnl !== undefined) {
            const todayPnl = parseFloat(stats.data.todayPnl);
            todayPnlElement.textContent = formatCurrency(todayPnl);
            
            if (todayPnl >= 0) {
                todayPnlElement.className = 'stat-value positive';
                todayPnlChangeElement.textContent = `+$${Math.abs(todayPnl).toFixed(2)} today`;
                todayPnlChangeElement.className = 'stat-change positive';
            } else {
                todayPnlElement.className = 'stat-value negative';
                todayPnlChangeElement.textContent = `-$${Math.abs(todayPnl).toFixed(2)} today`;
                todayPnlChangeElement.className = 'stat-change negative';
            }
        }
        
        // Active orders
        const activeOrdersElement = document.getElementById('active-orders');
        if (stats.data.activeOrders !== undefined) {
            activeOrdersElement.textContent = stats.data.activeOrders;
        }
    }

    /**
     * Update recent activity in UI
     */
    updateRecentActivity(activity) {
        if (!activity.success || !activity.data) return;
        
        const activityList = document.getElementById('activity-list');
        const activities = activity.data.slice(0, 10); // Show only latest 10
        
        activityList.innerHTML = activities.map(act => {
            const iconClass = this.getActivityIconClass(act.type);
            const icon = this.getActivityIcon(act.type);
            const time = this.formatRelativeTime(act.timestamp);
            
            return `
                <div class="activity-item">
                    <div class="activity-icon ${iconClass}">
                        <i class="${icon}"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">${act.message}</div>
                        <div class="activity-time">${time}</div>
                    </div>
                </div>
            `;
        }).join('');
    }

    /**
     * Get activity icon class
     */
    getActivityIconClass(type) {
        const classes = {
            'BUY': 'status-online',
            'SELL': 'status-danger',
            'SYSTEM': 'status-info',
            'ALERT': 'status-warning',
            'TRADE': 'status-info'
        };
        return classes[type] || 'status-info';
    }

    /**
     * Get activity icon
     */
    getActivityIcon(type) {
        const icons = {
            'BUY': 'fas fa-arrow-up',
            'SELL': 'fas fa-arrow-down',
            'SYSTEM': 'fas fa-cog',
            'ALERT': 'fas fa-bell',
            'TRADE': 'fas fa-exchange-alt'
        };
        return icons[type] || 'fas fa-info-circle';
    }

    /**
     * Format relative time
     */
    formatRelativeTime(timestamp) {
        const now = new Date();
        const time = new Date(timestamp);
        const diff = now - time;
        
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);
        
        if (minutes < 1) return 'Just now';
        if (minutes < 60) return `${minutes}m ago`;
        if (hours < 24) return `${hours}h ago`;
        if (days < 7) return `${days}d ago`;
        
        return time.toLocaleDateString();
    }

    /**
     * Refresh dashboard data
     */
    async refreshDashboardData() {
        const refreshBtn = document.getElementById('refresh-dashboard');
        const icon = refreshBtn.querySelector('i');
        
        // Show loading state
        icon.className = 'fas fa-sync-alt fa-spin';
        refreshBtn.disabled = true;
        
        try {
            await this.loadDashboardData();
            this.showToast('Dashboard data refreshed', 'success');
        } catch (error) {
            console.error('Error refreshing dashboard data:', error);
            this.showToast('Error refreshing dashboard data', 'error');
        } finally {
            // Reset button state
            icon.className = 'fas fa-sync-alt';
            refreshBtn.disabled = false;
        }
    }

    /**
     * Toggle emergency stop
     */
    async toggleEmergencyStop() {
        try {
            const response = await api.toggleEmergencyStop();
            
            if (response.success) {
                const config = response.data;
                const statusElement = document.getElementById('emergency-stop-status');
                const iconElement = document.getElementById('emergency-stop-icon');
                
                if (config.emergency_stop) {
                    statusElement.textContent = 'Stopped';
                    statusElement.className = 'stat-value negative';
                    iconElement.className = 'stat-icon status-warning';
                    this.showToast('Emergency stop activated', 'warning');
                } else {
                    statusElement.textContent = 'Active';
                    statusElement.className = 'stat-value positive';
                    iconElement.className = 'stat-icon status-online';
                    this.showToast('Emergency stop deactivated', 'success');
                }
            } else {
                this.showToast('Error toggling emergency stop', 'error');
            }
        } catch (error) {
            console.error('Error toggling emergency stop:', error);
            this.showToast('Error toggling emergency stop', 'error');
        }
    }

    /**
     * Test webhook
     */
    async testWebhook() {
        const testData = {
            coin: 'BTC',
            action: 'BUY',
            market_order: '1'
        };
        
        try {
            const response = await api.post('/webhook', testData, {
                'X-Webhook-Secret': 'itsMike818!'
            });
            
            if (response.success) {
                this.showToast('Webhook test successful', 'success');
            } else {
                this.showToast('Webhook test failed', 'error');
            }
        } catch (error) {
            console.error('Webhook test error:', error);
            this.showToast('Webhook test failed', 'error');
        }
    }

    /**
     * Start auto-refresh
     */
    startAutoRefresh() {
        this.updateInterval = setInterval(() => {
            if (document.visibilityState === 'visible') {
                this.loadDashboardData();
            }
        }, 30000); // Refresh every 30 seconds
    }

    /**
     * Stop auto-refresh
     */
    stopAutoRefresh() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
    }

    /**
     * Create modal
     */
    createModal(title, content) {
        return {
            title: title,
            content: content,
            size: 'medium'
        };
    }

    /**
     * Show modal
     */
    showModal(modal) {
        const modalHtml = `
            <div class="modal-overlay" onclick="dashboard.closeModal()"></div>
            <div class="modal active">
                <div class="modal-header">
                    <h3>${modal.title}</h3>
                    <button class="modal-close" onclick="dashboard.closeModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    ${modal.content}
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', modalHtml);
    }

    /**
     * Close modal
     */
    closeModal() {
        const modal = document.querySelector('.modal.active');
        if (modal) {
            modal.classList.remove('active');
            const overlay = modal.previousElementSibling;
            if (overlay && overlay.classList.contains('modal-overlay')) {
                overlay.remove();
            }
            setTimeout(() => modal.remove(), 300);
        }
    }

    /**
     * Set up tab switching
     */
    setupTabSwitching() {
        const tabButtons = document.querySelectorAll('.tab-btn');
        tabButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const targetTab = btn.dataset.tab;
                
                // Update button states
                tabButtons.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                
                // Update content visibility
                const contents = document.querySelectorAll('.tab-content');
                contents.forEach(content => {
                    content.classList.remove('active');
                    if (content.id === targetTab) {
                        content.classList.add('active');
                    }
                });
            });
        });
    }

    /**
     * Clean up
     */
    destroy() {
        this.stopAutoRefresh();
        
        // Destroy chart instances
        Object.keys(this.chartInstances).forEach(key => {
            if (this.chartInstances[key]) {
                this.chartInstances[key].destroy();
            }
        });
        
        this.chartInstances = {};
    }
}

// Initialize dashboard when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.dashboard = new DashboardManager();
});

// Export for use in modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DashboardManager;
}