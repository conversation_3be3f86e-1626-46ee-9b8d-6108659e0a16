/* ===== CSS Variables for Theme Management ===== */
:root {
    /* Light Theme Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --card-bg: #ffffff;
    --border-color: #e2e8f0;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --accent-primary: #3b82f6;
    --accent-secondary: #10b981;
    --accent-danger: #ef4444;
    --accent-warning: #f59e0b;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    
    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 300ms ease-in-out;
    --transition-slow: 500ms ease-in-out;
    
    /* Typography */
    --font-family-base: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    
    /* Spacing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 0.75rem;
    --spacing-base: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* Border Radius */
    --radius-sm: 6px;
    --radius-base: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    --radius-full: 9999px;
}

/* Dark Theme */
[data-theme="dark"] {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --card-bg: #1e293b;
    --border-color: #334155;
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
    --accent-primary: #60a5fa;
    --accent-secondary: #34d399;
    --accent-danger: #f87171;
    --accent-warning: #fbbf24;
    
    /* Dark mode shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4);
}

/* ===== Global Reset & Base Styles ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    line-height: 1.6;
}

body {
    font-family: var(--font-family-base);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    transition: background-color var(--transition-normal), color var(--transition-normal);
    overflow-x: hidden;
}

/* ===== Typography ===== */
h1, h2, h3, h4, h5, h6 {
    line-height: 1.2;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-base);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
    margin-bottom: var(--spacing-base);
    color: var(--text-secondary);
    line-height: 1.6;
}

/* ===== Layout ===== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-base);
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 calc(-1 * var(--spacing-base));
}

.col {
    flex: 1;
    padding: var(--spacing-base);
}

/* ===== Navigation ===== */
.navbar {
    background-color: var(--card-bg);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
    background-color: rgba(255, 255, 255, 0.9);
}

[data-theme="dark"] .navbar {
    background-color: rgba(30, 41, 59, 0.9);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-base);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 64px;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.nav-logo-img {
    width: 32px;
    height: 32px;
}

.nav-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
}

.nav-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.nav-badge.status-online {
    background-color: rgba(16, 185, 129, 0.1);
    color: #10b981;
}

.nav-badge.status-offline {
    background-color: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.nav-badge.status-warning {
    background-color: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
}

.nav-badge.status-info {
    background-color: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}

.nav-links {
    display: flex;
    gap: var(--spacing-base);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-base);
    text-decoration: none;
    color: var(--text-secondary);
    border-radius: var(--radius-base);
    font-weight: 500;
    transition: all var(--transition-fast);
}

.nav-link:hover {
    color: var(--text-primary);
    background-color: var(--bg-tertiary);
}

.nav-link.active {
    color: var(--accent-primary);
    background-color: rgba(59, 130, 246, 0.1);
}

.nav-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.theme-toggle,
.refresh-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: var(--radius-base);
    background-color: var(--bg-tertiary);
    color: var(--text-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
}

.theme-toggle:hover,
.refresh-btn:hover {
    background-color: var(--border-color);
    color: var(--text-primary);
}

/* ===== Main Content ===== */
.main-content {
    min-height: calc(100vh - 64px);
    background-color: var(--bg-primary);
}

.page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-xl) 0;
}

.page-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.page-subtitle {
    color: var(--text-secondary);
    font-size: var(--font-size-lg);
}

.header-actions {
    display: flex;
    gap: var(--spacing-sm);
}

/* ===== Forms ===== */
.form-group {
    margin-bottom: var(--spacing-base);
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

.form-control {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-base);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-base);
    background-color: var(--card-bg);
    color: var(--text-primary);
    font-size: var(--font-size-base);
    transition: all var(--transition-fast);
}

.form-control:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-select {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-base);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-base);
    background-color: var(--card-bg);
    color: var(--text-primary);
    font-size: var(--font-size-base);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.form-select:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-select-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
}

/* ===== Buttons ===== */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-base);
    border: none;
    border-radius: var(--radius-base);
    font-weight: 500;
    font-size: var(--font-size-base);
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    white-space: nowrap;
}

.btn-primary {
    background-color: var(--accent-primary);
    color: white;
}

.btn-primary:hover {
    background-color: #2563eb;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

.btn-secondary:hover {
    background-color: var(--border-color);
}

.btn-success {
    background-color: var(--accent-secondary);
    color: white;
}

.btn-success:hover {
    background-color: #059669;
}

.btn-danger {
    background-color: var(--accent-danger);
    color: white;
}

.btn-danger:hover {
    background-color: #dc2626;
}

.btn-warning {
    background-color: var(--accent-warning);
    color: white;
}

.btn-warning:hover {
    background-color: #d97706;
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.btn-lg {
    padding: var(--spacing-base) var(--spacing-lg);
    font-size: var(--font-size-lg);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* ===== Cards ===== */
.stat-card {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    transition: all var(--transition-normal);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* ===== Tables ===== */
.table-container {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--card-bg);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.data-table th,
.data-table td {
    padding: var(--spacing-base);
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.data-table th {
    background-color: var(--bg-tertiary);
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.data-table td {
    color: var(--text-secondary);
}

.data-table tbody tr:hover {
    background-color: var(--bg-tertiary);
}

.text-center {
    text-align: center;
}

/* ===== Badges ===== */
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.badge-success {
    background-color: rgba(16, 185, 129, 0.1);
    color: #10b981;
}

.badge-danger {
    background-color: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.badge-warning {
    background-color: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
}

.badge-info {
    background-color: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}

.badge-buy {
    background-color: rgba(16, 185, 129, 0.1);
    color: #10b981;
}

.badge-sell {
    background-color: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

/* ===== Loading States ===== */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.loading-overlay.active {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    width: 48px;
    height: 48px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--accent-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-text {
    color: white;
    font-size: var(--font-size-lg);
    margin-top: var(--spacing-base);
    text-align: center;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== Toast Notifications ===== */
.toast-container {
    position: fixed;
    top: 80px;
    right: 20px;
    z-index: 1001;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.toast {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-base) var(--spacing-lg);
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    min-width: 300px;
    max-width: 500px;
    animation: slideIn var(--transition-normal) ease-out;
}

.toast-success {
    border-color: #10b981;
    background-color: rgba(16, 185, 129, 0.1);
}

.toast-error {
    border-color: #ef4444;
    background-color: rgba(239, 68, 68, 0.1);
}

.toast-warning {
    border-color: #f59e0b;
    background-color: rgba(245, 158, 11, 0.1);
}

.toast-info {
    border-color: #3b82f6;
    background-color: rgba(59, 130, 246, 0.1);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* ===== Responsive Design ===== */
@media (max-width: 1024px) {
    .nav-container {
        flex-direction: column;
        height: auto;
        padding: var(--spacing-base);
        gap: var(--spacing-base);
    }
    
    .nav-links {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .nav-controls {
        align-self: flex-end;
    }
    
    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-base);
    }
}

@media (max-width: 768px) {
    .nav-container {
        padding: var(--spacing-sm);
    }
    
    .nav-title {
        font-size: var(--font-size-base);
    }
    
    .nav-link {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: var(--font-size-sm);
    }
    
    .page-title {
        font-size: var(--font-size-2xl);
    }
    
    .container {
        padding: 0 var(--spacing-sm);
    }
    
    .stat-card {
        padding: var(--spacing-base);
    }
    
    .data-table th,
    .data-table td {
        padding: var(--spacing-sm);
        font-size: var(--font-size-sm);
    }
    
    .btn {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: var(--font-size-sm);
    }
    
    .toast {
        min-width: 250px;
        font-size: var(--font-size-sm);
    }
}

@media (max-width: 480px) {
    .nav-links {
        display: none;
    }
    
    .nav-title {
        font-size: var(--font-size-base);
    }
    
    .page-title {
        font-size: var(--font-size-xl);
    }
    
    .stat-value {
        font-size: 1.25rem;
    }
    
    .page-header {
        padding: var(--spacing-base) 0;
    }
}

/* ===== Utility Classes ===== */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }

.bg-primary { background-color: var(--bg-primary); }
.bg-secondary { background-color: var(--bg-secondary); }
.bg-tertiary { background-color: var(--bg-tertiary); }

.border-primary { border-color: var(--border-color); }

.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

.rounded-sm { border-radius: var(--radius-sm); }
.rounded { border-radius: var(--radius-base); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-full { border-radius: var(--radius-full); }

.hidden {
    display: none !important;
}

.invisible {
    visibility: hidden !important;
}

.opacity-0 { opacity: 0; }
.opacity-50 { opacity: 0.5; }
.opacity-100 { opacity: 1; }

.transition-all {
    transition: all var(--transition-normal);
}

.transition-fast {
    transition: all var(--transition-fast);
}

.transition-slow {
    transition: all var(--transition-slow);
}

.cursor-pointer { cursor: pointer; }
.cursor-not-allowed { cursor: not-allowed; }

.select-none {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.overflow-auto {
    overflow: auto;
}

.overflow-hidden {
    overflow: hidden;
}

.whitespace-nowrap {
    white-space: nowrap;
}

.text-left { text-align: left; }
.text-right { text-align: right; }
.text-center { text-align: center; }
.text-justify { text-align: justify; }

.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.uppercase { text-transform: uppercase; }
.lowercase { text-transform: lowercase; }
.capitalize { text-transform: capitalize; }

.leading-tight { line-height: 1.25; }
.leading-normal { line-height: 1.6; }
.leading-relaxed { line-height: 1.875; }

/* ===== Scrollbar Styling ===== */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}

/* ===== Focus Styles ===== */
*:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
}

*:focus-visible {
    outline: 2px solid var(--accent-primary);
    outline-offset: 2px;
}

/* ===== Print Styles ===== */
@media print {
    .navbar,
    .nav-controls,
    .header-actions,
    .btn,
    .toast-container,
    .loading-overlay {
        display: none !important;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
    
    .stat-card,
    .data-table {
        border: 1px solid #000 !important;
    }
}