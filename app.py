import os
import json
import logging
from datetime import datetime, timezone
from decimal import Decimal, ROUND_DOWN
from flask import Flask, request, jsonify
from binance.client import Client
from binance.exceptions import BinanceAPIException, BinanceOrderException
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def clear_logs():
    """Clear log files to start fresh"""
    log_files = [
        'app.log',  # If we're using file logging
        'trading_bot.log'  # If we're using file logging
    ]
    
    for log_file in log_files:
        try:
            if os.path.exists(log_file):
                with open(log_file, 'w') as f:
                    f.write(f"=== Log cleared at {datetime.now(timezone.utc).isoformat()} ===\n")
                print(f"✅ Cleared log file: {log_file}")
        except Exception as e:
            print(f"Warning: Could not clear log file {log_file}: {e}")

# Clear logs at startup
print("Clearing previous logs...")
clear_logs()

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'default-secret-key')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('trading_bot.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Initialize Binance client
try:
    client = Client(
        os.getenv('BINANCE_API_KEY'),
        os.getenv('BINANCE_SECRET_KEY'),
        tld='us'  # Important: Use Binance US
    )
    logger.info("Binance US client initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize Binance client: {e}")
    client = None

class TradingBot:
    def __init__(self, binance_client):
        self.client = binance_client
        
        # Initialize default values first
        self.min_order_sizes = {
            'BTCUSD': 0.00001,
            'BTCUSDT': 0.00001,
            'ETHUSD': 0.001,
            'ETHUSDT': 0.001,
        }
        
        # Default fixed quantities (fallback)
        self.fixed_quantities = {
            'BTCUSDT': 0.001,  # Default will be overridden by .env
            'ETHUSDT': 0.01,   # Default will be overridden by .env
            'BTCUSD': 0.001,   # Default will be overridden by .env
            'ETHUSD': 0.01,    # Default will be overridden by .env
        }
        
        # Maximum quantity limits
        self.max_quantities = {
            'BTCUSDT': 0.1,
            'ETHUSDT': 1.0,
            'BTCUSD': 0.1,
            'ETHUSD': 1.0,
        }
        
        # Load configuration from environment
        self.load_configuration()
    
    def load_configuration(self):
        """Load configuration from environment variables"""
        import os
        
        # Emergency stop
        self.emergency_stop = os.getenv('EMERGENCY_STOP', 'false').lower() == 'true'
        logger.info(f"Emergency stop: {self.emergency_stop}")
        
        # Allowed symbols
        allowed_symbols_str = os.getenv('ALLOWED_SYMBOLS', 'BTCUSDT,ETHUSDT,ETHUSD')
        self.allowed_symbols = [s.strip().upper() for s in allowed_symbols_str.split(',')]
        logger.info(f"Allowed symbols: {self.allowed_symbols}")
        
        # Fixed quantities (minimum viable amounts)
        self.fixed_quantities['BTCUSDT'] = float(os.getenv('FIXED_QUANTITY_BTCUSDT', '0.001'))
        self.fixed_quantities['ETHUSDT'] = float(os.getenv('FIXED_QUANTITY_ETHUSDT', '0.01'))
        self.fixed_quantities['BTCUSD'] = float(os.getenv('FIXED_QUANTITY_BTCUSD', '0.001'))
        self.fixed_quantities['ETHUSD'] = float(os.getenv('FIXED_QUANTITY_ETHUSD', '0.01'))
        
        logger.info(f"Fixed quantities: BTCUSDT={self.fixed_quantities['BTCUSDT']}, ETHUSDT={self.fixed_quantities['ETHUSDT']}")
        
        # Percentage configuration
        self.quantity_percentage = float(os.getenv('QUANTITY_PERCENTAGE', 5))
        logger.info(f"Quantity percentage: {self.quantity_percentage}%")
        
        # Maximum quantities
        self.max_quantities['BTCUSDT'] = float(os.getenv('MAX_QUANTITY_BTCUSDT', '0.1'))
        self.max_quantities['ETHUSDT'] = float(os.getenv('MAX_QUANTITY_ETHUSDT', '1.0'))
        self.max_quantities['BTCUSD'] = float(os.getenv('MAX_QUANTITY_BTCUSD', '0.1'))
        self.max_quantities['ETHUSD'] = float(os.getenv('MAX_QUANTITY_ETHUSD', '1.0'))
        
        logger.info(f"Max quantities: BTCUSDT={self.max_quantities['BTCUSDT']}, ETHUSDT={self.max_quantities['ETHUSDT']}")
    
    def validate_symbol(self, symbol):
        """Validate symbol against allowed symbols list"""
        if symbol not in self.allowed_symbols:
            logger.error(f"Symbol {symbol} not in allowed list: {self.allowed_symbols}")
            return False
        return True
    
    def check_emergency_stop(self):
        """Check if emergency stop is activated"""
        if self.emergency_stop:
            logger.error("Emergency stop is activated - trading disabled")
            return False
        return True
    
    def get_max_quantity(self, symbol):
        """Get maximum allowed quantity for a symbol"""
        return self.max_quantities.get(symbol, 0.01)  # Default 0.01 for other symbols
    
    def validate_quantity(self, quantity, symbol):
        """Validate quantity against maximum limits"""
        max_qty = self.get_max_quantity(symbol)
        if quantity > max_qty:
            logger.error(f"Quantity {quantity} exceeds maximum {max_qty} for {symbol}")
            return False
        return True
    
    def calculate_percentage_quantity(self, symbol, side):
        """Calculate quantity based on percentage of available balance"""
        try:
            logger.info(f"Calculating percentage quantity for {symbol} {side}")
            
            if side.upper() == 'BUY':
                # For buy orders, use quote asset (USDT/USD)
                if 'USDT' in symbol:
                    quote_asset = 'USDT'
                elif 'USD' in symbol:
                    quote_asset = 'USD'
                else:
                    logger.error(f"Invalid symbol format for buy order: {symbol}")
                    return 0.0
                
                balance = self.get_account_balance(quote_asset)
                usable_balance = balance * (self.quantity_percentage / 100)
                
                # Get current price
                ticker = self.client.get_symbol_ticker(symbol=symbol)
                current_price = float(ticker['price'])
                
                quantity = usable_balance / current_price
                logger.info(f"Buy calculation: balance={balance}, usable={usable_balance}, price={current_price}, quantity={quantity}")
                
            else:  # SELL
                # For sell orders, use base asset (BTC/ETH)
                base_asset = symbol.replace('USDT', '').replace('USD', '')
                balance = self.get_account_balance(base_asset)
                quantity = balance * (self.quantity_percentage / 100)
                logger.info(f"Sell calculation: balance={balance}, quantity={quantity}")
            
            # Apply maximum quantity limits
            max_qty = self.get_max_quantity(symbol)
            quantity = min(quantity, max_qty)
            
            # Apply minimum quantity and precision
            min_qty = self.min_order_sizes.get(symbol, 0.00001)
            quantity = max(quantity, min_qty)
            
            # Apply precision formatting
            quantity = self.apply_precision_rules(quantity, symbol)
            
            # Validate final quantity
            if not self.validate_quantity(quantity, symbol):
                logger.error(f"Quantity validation failed for {symbol}")
                return 0.0
            
            logger.info(f"Final percentage quantity: {quantity}")
            return quantity
            
        except Exception as e:
            logger.error(f"Error calculating percentage quantity: {e}")
            return 0.0
    
    def apply_precision_rules(self, quantity, symbol):
        """Apply precision rules and step size formatting"""
        try:
            # Get symbol precision from exchange info
            symbol_info = self.client.get_symbol_info(symbol)
            if symbol_info:
                # Find the LOT_SIZE filter
                for filter in symbol_info.get('filters', []):
                    if filter.get('filterType') == 'LOT_SIZE':
                        step_size = filter.get('stepSize', '0.000001')
                        print(f"DEBUG: Step size for {symbol}: {step_size}")
                        
                        # Calculate precision from step size
                        if '.' in step_size:
                            precision = len(step_size.split('.')[1])
                        else:
                            precision = 0
                        print(f"DEBUG: Precision: {precision}")
                        
                        # Round to step size first
                        step_size_float = float(step_size)
                        quantity = round(quantity / step_size_float) * step_size_float
                        print(f"DEBUG: Quantity after step size: {quantity}")
                        
                        # Format with proper precision
                        quantity_str = "{:.{}f}".format(quantity, precision)
                        quantity = float(quantity_str)
                        print(f"DEBUG: Final quantity: {quantity}")
                        
                        # Ensure no scientific notation in the final float conversion
                        if quantity < 0.00001:
                            quantity = float("{:.8f}".format(quantity).rstrip('0').rstrip('.') or '0.00001')
                        break
            
            return quantity
            
        except Exception as e:
            print(f"DEBUG: Error in precision calculation: {e}")
            # Fallback to default precision
            if symbol.startswith('BTC'):
                quantity = round(quantity, 6)
            else:
                quantity = round(quantity, 4)
            return quantity
        
    def get_account_balance(self, asset):
        """Get available balance for specific asset"""
        try:
            account = self.client.get_account()
            for balance in account['balances']:
                if balance['asset'] == asset:
                    return float(balance['free'])
            return 0.0
        except Exception as e:
            logger.error(f"Error getting balance for {asset}: {e}")
            return 0.0
    
    def calculate_quantity(self, symbol, side, percentage=90):
        """Calculate order quantity based on available balance"""
        try:
            if side.upper() == 'BUY':
                # For buy orders, use quote asset (USDT/USD)
                if 'USDT' in symbol:
                    quote_asset = 'USDT'
                elif 'USD' in symbol:
                    quote_asset = 'USD'
                
                balance = self.get_account_balance(quote_asset)
                # Use percentage of available balance
                usable_balance = balance * (percentage / 100)
                
                # Get current price
                ticker = self.client.get_symbol_ticker(symbol=symbol)
                current_price = float(ticker['price'])
                
                # Calculate quantity
                quantity = usable_balance / current_price
                
            else:  # SELL
                # For sell orders, use base asset (BTC/ETH)
                base_asset = symbol.replace('USDT', '').replace('USD', '')
                quantity = self.get_account_balance(base_asset) * (percentage / 100)
            
            # Round down to avoid insufficient balance errors
            min_qty = self.min_order_sizes.get(symbol, 0.00001)
            quantity = max(quantity, min_qty)
            
            # Round to appropriate decimal places
            if symbol.startswith('BTC'):
                quantity = float(Decimal(str(quantity)).quantize(Decimal('0.00001'), rounding=ROUND_DOWN))
            else:
                quantity = float(Decimal(str(quantity)).quantize(Decimal('0.001'), rounding=ROUND_DOWN))
            
            # Ensure quantity is properly formatted for Binance API
            # Get symbol precision from exchange info
            try:
                symbol_info = self.client.get_symbol_info(symbol)
                if symbol_info:
                    # Find the LOT_SIZE filter
                    for filter in symbol_info.get('filters', []):
                        if filter.get('filterType') == 'LOT_SIZE':
                            step_size = filter.get('stepSize', '0.000001')
                            print(f"DEBUG: Step size for {symbol}: {step_size}")
                            
                            # Calculate precision from step size
                            if '.' in step_size:
                                precision = len(step_size.split('.')[1])
                            else:
                                precision = 0
                            print(f"DEBUG: Precision: {precision}")
                            
                            # Round to step size first
                            step_size_float = float(step_size)
                            quantity = round(quantity / step_size_float) * step_size_float
                            print(f"DEBUG: Quantity after step size: {quantity}")
                            
                            # Format with proper precision
                            quantity_str = "{:0.{}f}".format(quantity, precision)
                            quantity = float(quantity_str)
                            print(f"DEBUG: Final quantity: {quantity}")
                            
                            # Ensure no scientific notation in the final float conversion
                            if quantity < 0.00001:
                                quantity = float("{:.8f}".format(quantity).rstrip('0').rstrip('.') or '0.00001')
                            break
            except Exception as e:
                print(f"DEBUG: Error in precision calculation: {e}")
                # Fallback to default precision
                if symbol.startswith('BTC'):
                    quantity = round(quantity, 6)
                else:
                    quantity = round(quantity, 4)
                
            return quantity
            
        except Exception as e:
            logger.error(f"Error calculating quantity: {e}")
            return 0.0
    
    def place_market_order(self, symbol, side, custom_quantity=None, use_percentage=False):
        """Place market order on Binance US with enhanced configuration"""
        try:
            logger.info(f"Starting place_market_order for {symbol} {side}")
            
            if not self.client:
                raise Exception("Binance client not initialized")
            
            # Check emergency stop
            if not self.check_emergency_stop():
                raise Exception("Emergency stop activated - trading disabled")
            
            # Validate symbol
            if not self.validate_symbol(symbol):
                raise Exception(f"Symbol {symbol} not allowed")
            
            logger.info("Client initialized successfully")
            
            # Calculate quantity based on configuration
            quantity = 0.0
            if custom_quantity:
                # Use custom quantity provided in webhook
                quantity = float(custom_quantity)
                if quantity <= 0:
                    raise Exception(f"Invalid quantity: {quantity}")
                logger.info(f"Using custom quantity: {quantity}")
                
            elif use_percentage:
                # Use percentage-based calculation
                quantity = self.calculate_percentage_quantity(symbol, side)
                if quantity <= 0:
                    raise Exception(f"Invalid percentage quantity: {quantity}")
                logger.info(f"Using percentage quantity: {quantity}")
                
            else:
                # Use fixed quantity (current safe approach)
                if symbol in self.fixed_quantities:
                    quantity = self.fixed_quantities[symbol]
                else:
                    quantity = 0.00001  # Default fallback
                
                logger.info(f"Using fixed quantity: {quantity}")
            
            # Validate quantity
            if quantity <= 0:
                raise Exception(f"Invalid quantity: {quantity}")
            
            # Validate against maximum limits
            if not self.validate_quantity(quantity, symbol):
                raise Exception(f"Quantity {quantity} exceeds maximum allowed")
            
            # Apply precision formatting
            quantity = self.apply_precision_rules(quantity, symbol)
            
            logger.info(f"Final calculated quantity: {quantity}")
            
            # Ensure quantity is formatted properly (no scientific notation)
            quantity_str = "{:.8f}".format(quantity)
            if '.' in quantity_str:
                quantity_str = quantity_str.rstrip('0').rstrip('.')
            if not quantity_str or quantity_str == '0':
                quantity_str = '0.00001'
            
            logger.info(f"Final quantity string: '{quantity_str}'")
            
            # Place market order
            if side.upper() == 'BUY':
                logger.info("Placing BUY order")
                order = self.client.order_market_buy(
                    symbol=symbol,
                    quantity=quantity_str
                )
            else:
                logger.info("Placing SELL order")
                order = self.client.order_market_sell(
                    symbol=symbol,
                    quantity=quantity_str
                )
            
            logger.info(f"Order successful: {order['orderId']}")
            return {
                'success': True,
                'order_id': order['orderId'],
                'symbol': symbol,
                'side': side,
                'quantity': quantity,
                'status': order['status']
            }
            
        except BinanceAPIException as e:
            logger.error(f"Binance API error: {e}")
            return {'success': False, 'error': f'Binance API error: {e}'}
        except BinanceOrderException as e:
            logger.error(f"Binance order error: {e}")
            return {'success': False, 'error': f'Order error: {e}'}
        except Exception as e:
            logger.error(f"General error: {e}")
            return {'success': False, 'error': str(e)}

# Initialize trading bot
trading_bot = TradingBot(client) if client else None

@app.route('/', methods=['GET'])
def index():
    """Health check endpoint"""
    return jsonify({
        'status': 'active',
        'message': 'TradingView → Binance US Webhook Bot',
        'timestamp': datetime.now(timezone.utc).isoformat(),
        'binance_status': 'connected' if client else 'disconnected'
    })

@app.route('/webhook', methods=['POST'])
def webhook():
    """Main webhook endpoint for TradingView alerts"""
    try:
        # Get webhook data
        data = request.get_json()
        
        if not data:
            logger.warning("No JSON data received")
            return jsonify({'error': 'No JSON data received'}), 400
        
        logger.info(f"Received webhook data: {json.dumps(data, indent=2)}")
        
        # Validate webhook secret (optional security)
        import urllib.parse
        webhook_secret = request.headers.get('X-Webhook-Secret')
        if webhook_secret:
            webhook_secret = urllib.parse.unquote(webhook_secret)
        expected_secret = os.getenv('WEBHOOK_SECRET', 'testsecret')  # Default for testing
        
        logger.info(f"Webhook secret: '{webhook_secret}', Expected: '{expected_secret}'")
        
        if expected_secret and webhook_secret != expected_secret:
            logger.warning(f"Secret mismatch - Received: '{webhook_secret}', Expected: '{expected_secret}'")
            logger.warning("Invalid webhook secret")
            return jsonify({'error': 'Unauthorized'}), 401
        
        # Extract required fields - support both old and new formats
        action = data.get('action', '').upper()
        symbol = data.get('symbol', '').upper()
        coin = data.get('coin', '').upper()
        
        # Debug prints to console
        print(f"DEBUG: action='{action}', symbol='{symbol}', coin='{coin}'")
        
        # Handle new Pine Script format
        if coin and not symbol:
            # New format: {"coin":"BTC", "action":"BUY", "market_order":"1"}
            symbol = coin + "USD"  # Convert BTC to BTCUSD
            message = f"Pine Script alert - {action}"
            print(f"DEBUG: New format detected - symbol set to '{symbol}'")
        elif not coin and not symbol:
            print("DEBUG: Missing both symbol and coin")
            return jsonify({'error': 'Missing symbol or coin'}), 400
        
        if not action:
            print("DEBUG: Missing action")
            return jsonify({'error': 'Missing action'}), 400
        
        if action not in ['BUY', 'SELL']:
            print(f"DEBUG: Invalid action: {action}")
            return jsonify({'error': 'Invalid action. Use BUY or SELL'}), 400
        
        print(f"DEBUG: Validation passed - action='{action}', symbol='{symbol}'")
        
        # Optional: Get custom quantity
        quantity = data.get('quantity')
        
        # Execute trade
        if not trading_bot:
            return jsonify({'error': 'Trading bot not initialized'}), 500
        
  # Always use percentage-based trading from .env configuration
        use_percentage = True
        custom_quantity = data.get('quantity')
        
        logger.info(f"Executing trade - action: {action}, symbol: {symbol}, use_percentage: {use_percentage}, custom_quantity: {custom_quantity}")
        
        # Get current market price for verification
        try:
            ticker = trading_bot.client.get_symbol_ticker(symbol=symbol)
            current_price = float(ticker['price'])
            logger.info(f"Current market price for {symbol}: ${current_price}")
        except Exception as e:
            logger.warning(f"Could not get current price: {e}")
            current_price = None
        
        # Place the market order
        result = trading_bot.place_market_order(
            symbol=symbol, 
            side=action, 
            custom_quantity=custom_quantity,
            use_percentage=use_percentage
        )
        
        # Add price information to result
        if current_price:
            result['market_price'] = current_price
        
        if result['success']:
            response = {
                'status': 'success',
                'message': f'{action} order executed successfully',
                'order_details': result,
                'market_price': current_price,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            return jsonify(response), 200
        else:
            response = {
                'status': 'error',
                'message': 'Order execution failed',
                'error': result['error'],
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            return jsonify(response), 400
        
    except Exception as e:
        logger.error(f"Webhook error: {e}")
        return jsonify({
            'status': 'error',
            'message': 'Internal server error',
            'error': str(e),
            'timestamp': datetime.now(timezone.utc).isoformat()
        }), 500

@app.route('/test', methods=['POST'])
def test_order():
    """Test endpoint for manual testing"""
    try:
        data = request.get_json() or {}
        action = data.get('action', 'BUY').upper()
        symbol = data.get('symbol', 'BTCUSDT').upper()
        
        if not trading_bot:
            return jsonify({'error': 'Trading bot not initialized'}), 500
        
        # Place test order with percentage-based quantity
        result = trading_bot.place_market_order(symbol, action, use_percentage=True)
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Test order error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/balance', methods=['GET'])
def get_balance():
    """Get account balance"""
    try:
        if not client:
            return jsonify({'error': 'Binance client not initialized'}), 500
        
        account = client.get_account()
        balances = []
        
        for balance in account['balances']:
            if float(balance['free']) > 0 or float(balance['locked']) > 0:
                balances.append({
                    'asset': balance['asset'],
                    'free': balance['free'],
                    'locked': balance['locked']
                })
        
        return jsonify({
            'balances': balances,
            'timestamp': datetime.now(timezone.utc).isoformat()
        })
        
    except Exception as e:
        logger.error(f"Balance error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/config', methods=['GET'])
def get_config():
    """Get current trading configuration"""
    try:
        if not trading_bot:
            return jsonify({'error': 'Trading bot not initialized'}), 500
        
        config = {
            'emergency_stop': trading_bot.emergency_stop,
            'allowed_symbols': trading_bot.allowed_symbols,
            'quantity_percentage': trading_bot.quantity_percentage,
            'max_quantities': trading_bot.max_quantities,
            'fixed_quantities': trading_bot.fixed_quantities,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        
        return jsonify(config)
        
    except Exception as e:
        logger.error(f"Config error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/emergency-stop', methods=['POST'])
def emergency_stop():
    """Toggle emergency stop (admin only)"""
    try:
        if not trading_bot:
            return jsonify({'error': 'Trading bot not initialized'}), 500
        
        # Toggle emergency stop
        trading_bot.emergency_stop = not trading_bot.emergency_stop
        
        return jsonify({
            'success': True,
            'emergency_stop': trading_bot.emergency_stop,
            'message': 'Emergency stop ' + ('activated' if trading_bot.emergency_stop else 'deactivated'),
            'timestamp': datetime.now(timezone.utc).isoformat()
        })
        
    except Exception as e:
        logger.error(f"Emergency stop error: {e}")
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 5000))
    app.run(host='0.0.0.0', port=port, debug=False)