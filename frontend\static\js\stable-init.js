/**
 * Stable initialization script to prevent JavaScript errors
 */

// Global error handler
window.addEventListener('error', function(e) {
    console.warn('Global error caught:', e.message);
    return false;
});

// Unhandled promise rejection handler
window.addEventListener('unhandledrejection', function(e) {
    console.warn('Unhandled promise rejection:', e.reason);
    return false;
});

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('Initializing stable application...');
    
    // Initialize theme safely
    initThemeSafely();
    
    // Initialize navigation safely
    initNavigationSafely();
    
    // Initialize dashboard data safely
    setTimeout(initDashboardSafely, 1000);
});

/**
 * Initialize theme safely
 */
function initThemeSafely() {
    try {
        const savedTheme = localStorage.getItem('theme') || 'light';
        document.documentElement.setAttribute('data-theme', savedTheme);
        
        // Update theme toggle if it exists
        const themeToggle = document.getElementById('themeToggle') || document.getElementById('theme-toggle');
        if (themeToggle) {
            const lightIcon = themeToggle.querySelector('.fa-sun, .theme-light-icon');
            const darkIcon = themeToggle.querySelector('.fa-moon, .theme-dark-icon');
            
            if (lightIcon && darkIcon) {
                if (savedTheme === 'dark') {
                    lightIcon.style.display = 'none';
                    darkIcon.style.display = 'inline';
                } else {
                    lightIcon.style.display = 'inline';
                    darkIcon.style.display = 'none';
                }
            }
            
            // Add click event
            themeToggle.addEventListener('click', function() {
                const currentTheme = document.documentElement.getAttribute('data-theme');
                const newTheme = currentTheme === 'light' ? 'dark' : 'light';
                document.documentElement.setAttribute('data-theme', newTheme);
                localStorage.setItem('theme', newTheme);
                
                // Update icons
                if (lightIcon && darkIcon) {
                    if (newTheme === 'dark') {
                        lightIcon.style.display = 'none';
                        darkIcon.style.display = 'inline';
                    } else {
                        lightIcon.style.display = 'inline';
                        darkIcon.style.display = 'none';
                    }
                }
            });
        }
    } catch (error) {
        console.warn('Theme initialization error:', error);
    }
}

/**
 * Initialize navigation safely
 */
function initNavigationSafely() {
    try {
        const navLinks = document.querySelectorAll('.nav-link, .mobile-nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                
                const href = this.getAttribute('href');
                if (href && href !== '#') {
                    // Update active state
                    navLinks.forEach(l => l.classList.remove('active'));
                    this.classList.add('active');
                    
                    // Navigate if it's a frontend route
                    if (href.startsWith('/')) {
                        window.location.href = href;
                    }
                }
            });
        });
    } catch (error) {
        console.warn('Navigation initialization error:', error);
    }
}

/**
 * Initialize dashboard data safely
 */
function initDashboardSafely() {
    try {
        // Hide loading overlay if it exists
        const loadingOverlay = document.getElementById('loadingOverlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
        }
        
        // Initialize connection status
        updateConnectionStatusSafely();
        
        // Load dashboard data with error handling
        loadDashboardDataSafely();
        
        // Set up periodic updates
        setInterval(loadDashboardDataSafely, 30000); // Update every 30 seconds
        
    } catch (error) {
        console.warn('Dashboard initialization error:', error);
    }
}

/**
 * Update connection status safely
 */
function updateConnectionStatusSafely() {
    try {
        const statusElements = document.querySelectorAll('.connection-indicator, .connection-status');
        statusElements.forEach(element => {
            if (element) {
                const statusDot = element.querySelector('.status-dot');
                const statusText = element.querySelector('.status-text');
                
                if (statusDot) {
                    statusDot.className = 'status-dot active';
                }
                if (statusText) {
                    statusText.textContent = 'Connected';
                    statusText.className = 'status-text connected';
                }
            }
        });
    } catch (error) {
        console.warn('Connection status update error:', error);
    }
}

/**
 * Load dashboard data safely
 */
async function loadDashboardDataSafely() {
    try {
        // Hide any loading indicators
        hideAllLoadingIndicators();
        
        // Load system status
        await loadSystemStatusSafely();
        
        // Load dashboard stats
        loadDashboardStatsSafely();
        
        // Load recent activity
        loadRecentActivitySafely();
        
    } catch (error) {
        console.warn('Dashboard data loading error:', error);
        // Don't show errors to user, just continue
    }
}

/**
 * Load system status safely
 */
async function loadSystemStatusSafely() {
    try {
        if (window.api) {
            const response = await window.api.healthCheck();
            if (response.success) {
                updateConnectionStatusSafely();
            }
        }
    } catch (error) {
        console.warn('System status loading error:', error);
        // Show disconnected status but don't break the UI
        updateConnectionStatusSafely(false);
    }
}

/**
 * Load dashboard stats safely
 */
function loadDashboardStatsSafely() {
    try {
        if (window.api) {
            window.api.getBalance().then(response => {
                if (response.success) {
                    updateBalanceDisplaySafely(response.data);
                }
            }).catch(error => {
                console.warn('Balance loading error:', error);
            });
            
            window.api.getTradingStats().then(response => {
                if (response.success) {
                    updateStatsDisplaySafely(response.data);
                }
            }).catch(error => {
                console.warn('Stats loading error:', error);
            });
        }
    } catch (error) {
        console.warn('Dashboard stats loading error:', error);
    }
}

/**
 * Load recent activity safely
 */
function loadRecentActivitySafely() {
    try {
        if (window.api) {
            window.api.getRecentActivity(10).then(response => {
                if (response.success) {
                    updateActivityDisplaySafely(response.data);
                }
            }).catch(error => {
                console.warn('Activity loading error:', error);
            });
        }
    } catch (error) {
        console.warn('Activity loading error:', error);
    }
}

/**
 * Update balance display safely
 */
function updateBalanceDisplaySafely(data) {
    try {
        const balanceElement = document.getElementById('account-balance') || 
                              document.querySelector('[data-balance]') ||
                              document.querySelector('.stat-card .stat-value');
        
        if (balanceElement && data && data.total_value) {
            balanceElement.textContent = `$${parseFloat(data.total_value).toFixed(2)}`;
        }
    } catch (error) {
        console.warn('Balance display update error:', error);
    }
}

/**
 * Update stats display safely
 */
function updateStatsDisplaySafely(data) {
    try {
        if (data) {
            const elements = {
                'total-trades': data.totalTrades || 0,
                'winning-trades': data.winningTrades || 0,
                'losing-trades': data.losingTrades || 0,
                'win-rate': data.winRate ? `${data.winRate}%` : '0%'
            };
            
            Object.keys(elements).forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = elements[id];
                }
            });
        }
    } catch (error) {
        console.warn('Stats display update error:', error);
    }
}

/**
 * Update activity display safely
 */
function updateActivityDisplaySafely(data) {
    try {
        const activityList = document.getElementById('activity-list') || 
                            document.querySelector('.activity-feed') ||
                            document.querySelector('#recent-trades-tbody');
        
        if (activityList && data && data.length > 0) {
            // Clear existing content
            activityList.innerHTML = '';
            
            // Add recent activities
            data.slice(0, 5).forEach(activity => {
                const activityItem = createActivityItemSafely(activity);
                if (activityItem) {
                    activityList.appendChild(activityItem);
                }
            });
        }
    } catch (error) {
        console.warn('Activity display update error:', error);
    }
}

/**
 * Create activity item safely
 */
function createActivityItemSafely(activity) {
    try {
        const item = document.createElement('div');
        item.className = 'activity-item';
        
        const icon = getActivityIconSafely(activity.type);
        const time = new Date(activity.timestamp).toLocaleTimeString();
        
        item.innerHTML = `
            <div class="activity-icon status-${activity.status.toLowerCase()}">
                <i class="${icon}"></i>
            </div>
            <div class="activity-content">
                <div class="activity-title">${activity.type} ${activity.symbol || ''}</div>
                <div class="activity-time">${time}</div>
            </div>
        `;
        
        return item;
    } catch (error) {
        console.warn('Activity item creation error:', error);
        return null;
    }
}

/**
 * Get activity icon safely
 */
function getActivityIconSafely(type) {
    const icons = {
        'BUY': 'fas fa-arrow-up',
        'SELL': 'fas fa-arrow-down',
        'SYSTEM': 'fas fa-cog',
        'ALERT': 'fas fa-bell',
        'TRADE': 'fas fa-exchange-alt'
    };
    return icons[type] || 'fas fa-info-circle';
}

/**
 * Hide all loading indicators
 */
function hideAllLoadingIndicators() {
    try {
        const loadingElements = document.querySelectorAll('.loading-indicator, #loadingIndicator, #loadingOverlay');
        loadingElements.forEach(element => {
            if (element && element.style) {
                element.style.display = 'none';
            }
        });
    } catch (error) {
        console.warn('Error hiding loading indicators:', error);
    }
}

console.log('Stable initialization script loaded');