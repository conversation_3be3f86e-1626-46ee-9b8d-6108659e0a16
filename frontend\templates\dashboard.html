{% extends "base.html" %}

{% block title %}Dashboard - <PERSON><PERSON><PERSON><PERSON>{% endblock %}

{% block head %}
<style>
    .dashboard-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .stat-card {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        padding: 1.5rem;
        transition: all 0.3s ease;
    }
    
    .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }
    
    .stat-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }
    
    .stat-title {
        color: var(--text-secondary);
        font-size: 0.875rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }
    
    .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
    }
    
    .stat-value {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }
    
    .stat-change {
        font-size: 0.875rem;
        font-weight: 500;
    }
    
    .status-online { background: linear-gradient(135deg, #10b981, #059669); color: white; }
    .status-warning { background: linear-gradient(135deg, #f59e0b, #d97706); color: white; }
    .status-offline { background: linear-gradient(135deg, #ef4444, #dc2626); color: white; }
    .status-info { background: linear-gradient(135deg, #3b82f6, #2563eb); color: white; }
    
    .positive { color: #10b981; }
    .negative { color: #ef4444; }
    .neutral { color: var(--text-secondary); }
    
    .activity-feed {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        padding: 1.5rem;
        height: 400px;
        overflow-y: auto;
    }
    
    .activity-item {
        display: flex;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid var(--border-color);
    }
    
    .activity-item:last-child {
        border-bottom: none;
    }
    
    .activity-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        flex-shrink: 0;
    }
    
    .activity-content {
        flex: 1;
    }
    
    .activity-title {
        font-weight: 500;
        margin-bottom: 0.25rem;
    }
    
    .activity-time {
        font-size: 0.75rem;
        color: var(--text-secondary);
    }
    
    .chart-container {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        padding: 1.5rem;
        height: 400px;
    }
    
    @media (max-width: 768px) {
        .dashboard-grid {
            grid-template-columns: 1fr;
        }
        
        .stat-value {
            font-size: 1.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Header -->
<div class="page-header">
    <div class="header-content">
        <h1 class="page-title">Dashboard</h1>
        <p class="page-subtitle">Real-time monitoring and trading overview</p>
    </div>
    <div class="header-actions">
        <button class="btn btn-secondary" id="refresh-dashboard">
            <i class="fas fa-sync-alt"></i> Refresh
        </button>
    </div>
</div>

<!-- Stats Grid -->
<div class="dashboard-grid">
    <!-- System Status -->
    <div class="stat-card">
        <div class="stat-header">
            <span class="stat-title">System Status</span>
            <div class="stat-icon status-info" id="system-status-icon">
                <i class="fas fa-server"></i>
            </div>
        </div>
        <div class="stat-value" id="system-status">Loading...</div>
        <div class="stat-change" id="system-status-change">Checking connection...</div>
    </div>
    
    <!-- Account Balance -->
    <div class="stat-card">
        <div class="stat-header">
            <span class="stat-title">Account Balance</span>
            <div class="stat-icon status-online">
                <i class="fas fa-wallet"></i>
            </div>
        </div>
        <div class="stat-value" id="account-balance">$0.00</div>
        <div class="stat-change neutral">Total portfolio value</div>
    </div>
    
    <!-- Today's P&L -->
    <div class="stat-card">
        <div class="stat-header">
            <span class="stat-title">Today's P&L</span>
            <div class="stat-icon status-online">
                <i class="fas fa-chart-line"></i>
            </div>
        </div>
        <div class="stat-value" id="today-pnl">$0.00</div>
        <div class="stat-change" id="today-pnl-change">Today's performance</div>
    </div>
    
    <!-- Active Orders -->
    <div class="stat-card">
        <div class="stat-header">
            <span class="stat-title">Active Orders</span>
            <div class="stat-icon status-warning">
                <i class="fas fa-exchange-alt"></i>
            </div>
        </div>
        <div class="stat-value" id="active-orders">0</div>
        <div class="stat-change neutral">Pending executions</div>
    </div>
    
    <!-- Emergency Stop -->
    <div class="stat-card">
        <div class="stat-header">
            <span class="stat-title">Emergency Stop</span>
            <div class="stat-icon status-online" id="emergency-stop-icon">
                <i class="fas fa-shield-alt"></i>
            </div>
        </div>
        <div class="stat-value" id="emergency-stop-status">Active</div>
        <div class="stat-change neutral">Trading enabled</div>
    </div>
    
    <!-- Webhook Status -->
    <div class="stat-card">
        <div class="stat-header">
            <span class="stat-title">Webhook Status</span>
            <div class="stat-icon status-online">
                <i class="fas fa-plug"></i>
            </div>
        </div>
        <div class="stat-value" id="webhook-status">Active</div>
        <div class="stat-change neutral">Ready for alerts</div>
    </div>
</div>

<!-- Charts Grid -->
<div class="dashboard-grid" style="grid-template-columns: 1fr 1fr;">
    <!-- Performance Chart -->
    <div class="chart-container">
        <div class="stat-header">
            <span class="stat-title">Performance Overview</span>
            <select class="form-select form-select-sm" id="chart-period">
                <option value="1d">24 Hours</option>
                <option value="7d">7 Days</option>
                <option value="30d">30 Days</option>
            </select>
        </div>
        <canvas id="performance-chart"></canvas>
    </div>
    
    <!-- Volume Chart -->
    <div class="chart-container">
        <div class="stat-header">
            <span class="stat-title">Trading Volume</span>
            <div class="chart-controls">
                <i class="fas fa-chart-bar"></i>
            </div>
        </div>
        <canvas id="volume-chart"></canvas>
    </div>
</div>

<!-- Second Row Charts -->
<div class="dashboard-grid" style="grid-template-columns: 1fr 1fr;">
    <!-- Win Rate Chart -->
    <div class="chart-container">
        <div class="stat-header">
            <span class="stat-title">Win Rate Analysis</span>
            <div class="chart-controls">
                <i class="fas fa-percentage"></i>
            </div>
        </div>
        <canvas id="winrate-chart"></canvas>
    </div>
    
    <!-- Recent Activity -->
    <div class="activity-feed">
        <div class="stat-header">
            <span class="stat-title">Recent Activity</span>
            <button class="btn btn-sm btn-secondary" onclick="refreshActivity()">
                <i class="fas fa-sync-alt"></i>
            </button>
        </div>
        <div id="activity-list">
            <div class="activity-item">
                <div class="activity-icon status-info">
                    <i class="fas fa-info-circle"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">System initializing...</div>
                    <div class="activity-time">Just now</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Trades Table -->
<div class="stat-card" style="margin-top: 1.5rem;">
    <div class="stat-header">
        <span class="stat-title">Recent Trades</span>
        <button class="btn btn-sm btn-secondary" onclick="loadRecentTrades()">
            <i class="fas fa-sync-alt"></i> Refresh
        </button>
    </div>
    <div class="table-container">
        <table class="data-table">
            <thead>
                <tr>
                    <th>Time</th>
                    <th>Symbol</th>
                    <th>Side</th>
                    <th>Quantity</th>
                    <th>Price</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody id="recent-trades-tbody">
                <tr>
                    <td colspan="6" class="text-center">Loading recent trades...</td>
                </tr>
            </tbody>
        </table>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Initialize dashboard
    document.addEventListener('DOMContentLoaded', function() {
        initializeDashboard();
        loadDashboardData();
        
        // Set up auto-refresh
        setInterval(loadDashboardData, 30000); // Refresh every 30 seconds
    });
    
    async function initializeDashboard() {
        console.log('Initializing dashboard...');
        
        // Set up navigation
        setupNavigation();
        
        // Set up theme toggle
        setupThemeToggle();
        
        // Set up refresh button
        document.getElementById('refresh-dashboard').addEventListener('click', loadDashboardData);
        
        // Initialize performance chart
        initializePerformanceChart();
        
        // Show loading overlay
        showLoading();
    }
    
    async function loadDashboardData() {
        try {
            // Load system status
            await loadSystemStatus();
            
            // Load account balance
            await loadAccountBalance();
            
            // Load configuration
            await loadConfiguration();
            
            // Load recent activity
            await loadRecentActivity();
            
            // Load recent trades
            await loadRecentTrades();
            
        } catch (error) {
            console.error('Error loading dashboard data:', error);
            showToast('Error loading dashboard data', 'error');
        } finally {
            hideLoading();
        }
    }
    
    async function loadSystemStatus() {
        try {
            const response = await api.get('/');
            const data = await response.json();
            
            const statusElement = document.getElementById('system-status');
            const changeElement = document.getElementById('system-status-change');
            const iconElement = document.getElementById('system-status-icon');
            
            if (data.status === 'active') {
                statusElement.textContent = 'Online';
                statusElement.className = 'stat-value positive';
                changeElement.textContent = 'All systems operational';
                changeElement.className = 'stat-change positive';
                iconElement.className = 'stat-icon status-online';
            } else {
                statusElement.textContent = 'Offline';
                statusElement.className = 'stat-value negative';
                changeElement.textContent = 'System not responding';
                changeElement.className = 'stat-change negative';
                iconElement.className = 'stat-icon status-offline';
            }
            
            updateConnectionStatus(data.status === 'active');
            
        } catch (error) {
            console.error('Error loading system status:', error);
            document.getElementById('system-status').textContent = 'Error';
            document.getElementById('system-status').className = 'stat-value negative';
            document.getElementById('system-status-change').textContent = 'Connection failed';
            updateConnectionStatus(false);
        }
    }
    
    async function loadAccountBalance() {
        try {
            const response = await api.get('/balance');
            const data = await response.json();
            
            let totalBalance = 0;
            data.balances.forEach(balance => {
                totalBalance += parseFloat(balance.free);
            });
            
            document.getElementById('account-balance').textContent = formatCurrency(totalBalance);
            
        } catch (error) {
            console.error('Error loading account balance:', error);
            document.getElementById('account-balance').textContent = '$0.00';
        }
    }
    
    async function loadConfiguration() {
        try {
            const response = await api.get('/config');
            const data = await response.json();
            
            // Update emergency stop status
            const emergencyStopElement = document.getElementById('emergency-stop-status');
            const emergencyStopIcon = document.getElementById('emergency-stop-icon');
            
            if (data.emergency_stop) {
                emergencyStopElement.textContent = 'Stopped';
                emergencyStopElement.className = 'stat-value negative';
                emergencyStopIcon.className = 'stat-icon status-warning';
            } else {
                emergencyStopElement.textContent = 'Active';
                emergencyStopElement.className = 'stat-value positive';
                emergencyStopIcon.className = 'stat-icon status-online';
            }
            
        } catch (error) {
            console.error('Error loading configuration:', error);
        }
    }
    
    async function loadRecentActivity() {
        try {
            // This would typically load from backend logs
            // For now, we'll simulate recent activity
            const activityList = document.getElementById('activity-list');
            const activities = [
                {
                    icon: 'fas fa-check-circle',
                    iconClass: 'status-online',
                    title: 'System health check completed',
                    time: '2 minutes ago'
                },
                {
                    icon: 'fas fa-exchange-alt',
                    iconClass: 'status-info',
                    title: 'Trading bot ready for alerts',
                    time: '5 minutes ago'
                },
                {
                    icon: 'fas fa-plug',
                    iconClass: 'status-online',
                    title: 'Webhook endpoint active',
                    time: '10 minutes ago'
                }
            ];
            
            activityList.innerHTML = activities.map(activity => `
                <div class="activity-item">
                    <div class="activity-icon ${activity.iconClass}">
                        <i class="${activity.icon}"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">${activity.title}</div>
                        <div class="activity-time">${activity.time}</div>
                    </div>
                </div>
            `).join('');
            
        } catch (error) {
            console.error('Error loading recent activity:', error);
        }
    }
    
    async function loadRecentTrades() {
        try {
            const tbody = document.getElementById('recent-trades-tbody');
            tbody.innerHTML = '<tr><td colspan="6" class="text-center">Loading trades...</td></tr>';
            
            // This would typically load from a trades endpoint
            // For now, we'll simulate recent trades
            setTimeout(() => {
                const trades = [
                    {
                        time: '2025-09-11T21:54:19Z',
                        symbol: 'BTCUSDT',
                        side: 'BUY',
                        quantity: '0.001',
                        price: '56850.00',
                        status: 'FILLED'
                    },
                    {
                        time: '2025-09-11T21:45:12Z',
                        symbol: 'BTCUSD',
                        side: 'SELL',
                        quantity: '0.001',
                        price: '56845.00',
                        status: 'FILLED'
                    }
                ];
                
                tbody.innerHTML = trades.map(trade => `
                    <tr>
                        <td>${formatDateTime(trade.time)}</td>
                        <td>${trade.symbol}</td>
                        <td>
                            <span class="badge ${trade.side === 'BUY' ? 'badge-buy' : 'badge-sell'}">
                                ${trade.side}
                            </span>
                        </td>
                        <td>${trade.quantity}</td>
                        <td>$${formatNumber(trade.price)}</td>
                        <td>
                            <span class="badge badge-success">${trade.status}</span>
                        </td>
                    </tr>
                `).join('');
            }, 1000);
            
        } catch (error) {
            console.error('Error loading recent trades:', error);
            document.getElementById('recent-trades-tbody').innerHTML = 
                '<tr><td colspan="6" class="text-center">Error loading trades</td></tr>';
        }
    }
    
    function initializePerformanceChart() {
        const ctx = document.getElementById('performance-chart').getContext('2d');
        
        // Generate sample performance data
        const labels = [];
        const data = [];
        const now = new Date();
        
        for (let i = 23; i >= 0; i--) {
            const time = new Date(now.getTime() - i * 60 * 60 * 1000);
            labels.push(time.getHours() + ':00');
            data.push(Math.random() * 1000 - 500); // Random P&L values
        }
        
        window.performanceChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'P&L ($)',
                    data: data,
                    borderColor: '#10b981',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: 'var(--text-secondary)'
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: 'var(--text-secondary)'
                        }
                    }
                }
            }
        });
    }
    
    function updateConnectionStatus(isOnline) {
        const statusBadge = document.getElementById('connection-status');
        const statusText = statusBadge.querySelector('.status-text');
        const statusIcon = statusBadge.querySelector('i');
        
        if (isOnline) {
            statusBadge.className = 'nav-badge status-online';
            statusText.textContent = 'Connected';
            statusIcon.className = 'fas fa-circle';
        } else {
            statusBadge.className = 'nav-badge status-offline';
            statusText.textContent = 'Disconnected';
            statusIcon.className = 'fas fa-exclamation-circle';
        }
    }
    
    function setupNavigation() {
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Update active state
                navLinks.forEach(l => l.classList.remove('active'));
                this.classList.add('active');
                
                // Navigate to page (for single-page app, this would handle routing)
                const page = this.dataset.page;
                console.log('Navigating to:', page);
                
                // For now, just show toast
                showToast(`Navigating to ${page}`, 'info');
            });
        });
    }
    
    function setupThemeToggle() {
        const themeToggle = document.getElementById('themeToggle') || 
                           document.getElementById('theme-toggle');
        const body = document.body;
        
        if (!themeToggle) {
            console.warn('Theme toggle not found');
            return;
        }
        
        // Check for saved theme preference
        const savedTheme = localStorage.getItem('theme') || 'light';
        body.setAttribute('data-theme', savedTheme);
        updateThemeToggleIcon(savedTheme);
        
        themeToggle.addEventListener('click', function() {
            const currentTheme = body.getAttribute('data-theme');
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            
            body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            updateThemeToggleIcon(newTheme);
            
            showToast(`Switched to ${newTheme} theme`, 'info');
        });
    }
    
    function updateThemeToggleIcon(theme) {
        const themeToggle = document.getElementById('themeToggle') || 
                           document.getElementById('theme-toggle');
        if (!themeToggle) return;
        
        const icon = themeToggle.querySelector('i');
        if (icon) {
            icon.className = theme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
        }
    }
    
    // Utility functions
    function formatCurrency(amount) {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount);
    }
    
    function formatNumber(num) {
        return new Intl.NumberFormat('en-US').format(num);
    }
    
    function formatDateTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }
    
    function showLoading() {
        const loadingOverlay = document.getElementById('loadingOverlay') ||
                             document.getElementById('loading-overlay') ||
                             document.querySelector('.loading-overlay, .loadingOverlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'flex';
        }
    }
    
    function hideLoading() {
        const loadingOverlay = document.getElementById('loadingOverlay') ||
                             document.getElementById('loading-overlay') ||
                             document.querySelector('.loading-overlay, .loadingOverlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
        }
    }
    
    function showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        `;
        
        const container = document.getElementById('toast-container');
        container.appendChild(toast);
        
        setTimeout(() => {
            toast.remove();
        }, 5000);
    }
    
    // Make functions globally available
    window.refreshActivity = loadRecentActivity;
</script>
{% endblock %}