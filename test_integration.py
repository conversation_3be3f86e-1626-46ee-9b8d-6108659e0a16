#!/usr/bin/env python3
"""
Integration test script for TradingView Bot frontend
Tests frontend functionality and API integration
"""

import requests
import json
import time
from datetime import datetime

def test_backend_apis():
    """Test all backend API endpoints"""
    base_url = "https://web-production-0efa7.up.railway.app"
    
    endpoints = {
        'main': '/',
        'balance': '/balance',
        'config': '/config',
        'emergency_stop': '/emergency-stop',
        'test': '/test'
    }
    
    print("Testing Backend API Endpoints...")
    print("=" * 50)
    
    results = {}
    for name, endpoint in endpoints.items():
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=10)
            results[name] = {
                'status': response.status_code,
                'success': response.status_code == 200,
                'response_time': response.elapsed.total_seconds()
            }
            print(f"OK {name.upper()}: {response.status_code} ({response.elapsed.total_seconds():.2f}s)")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   Response: {json.dumps(data, indent=2)[:100]}...")
                except:
                    print(f"   Response: {response.text[:100]}...")
            else:
                print(f"   Error: {response.text[:100]}...")
                
        except Exception as e:
            results[name] = {
                'status': 'ERROR',
                'success': False,
                'error': str(e)
            }
            print(f"FAIL {name.upper()}: {e}")
        
        print("-" * 30)
    
    return results

def test_frontend_server():
    """Test frontend server functionality"""
    print("\nTesting Frontend Server...")
    print("=" * 50)
    
    try:
        # Import frontend app
        from app_frontend import app_frontend
        
        with app_frontend.test_client() as client:
            # Test main pages
            pages = [
                ('/', 'Dashboard'),
                ('/dashboard', 'Dashboard'),
                ('/trading', 'Trading'),
                ('/config', 'Config'),
                ('/logs', 'Logs')
            ]
            
            results = {}
            for path, name in pages:
                try:
                    response = client.get(path)
                    results[path] = {
                        'status': response.status_code,
                        'success': response.status_code == 200
                    }
                    print(f"OK {name}: {response.status_code}")
                except Exception as e:
                    results[path] = {
                        'status': 'ERROR',
                        'success': False,
                        'error': str(e)
                    }
                    print(f"FAIL {name}: {e}")
            
            # Test static files
            static_files = [
                '/static/js/core-api.js',
                '/static/js/unified-dashboard.js',
                '/static/css/main.css',
                '/static/images/logo.svg'
            ]
            
            print("\nTesting Static Files...")
            for file_path in static_files:
                try:
                    response = client.get(file_path)
                    print(f"OK {file_path}: {response.status_code}")
                except Exception as e:
                    print(f"FAIL {file_path}: {e}")
            
            return results
            
    except Exception as e:
        print(f"FAIL Frontend server test failed: {e}")
        return {}

def calculate_portfolio_value(balance_data):
    """Calculate portfolio value from balance data"""
    if not balance_data or 'balances' not in balance_data:
        return 0
    
    # Approximate prices (in production, you'd get real prices)
    prices = {
        'BTC': 50000,  # $50,000 per BTC
        'ETH': 3000,   # $3,000 per ETH
        'USDT': 1,     # $1 per USDT
        'USD': 1       # $1 per USD
    }
    
    total_value = 0
    for asset in balance_data['balances']:
        asset_symbol = asset['asset']
        free_amount = float(asset['free'])
        locked_amount = float(asset['locked'])
        
        total_amount = free_amount + locked_amount
        if asset_symbol in prices:
            total_value += total_amount * prices[asset_symbol]
    
    return total_value

def generate_test_report(backend_results, frontend_results):
    """Generate comprehensive test report"""
    print("\n[INTEGRATION TEST REPORT]")
    print("=" * 50)
    
    # Backend summary
    backend_success = sum(1 for r in backend_results.values() if r.get('success', False))
    backend_total = len(backend_results)
    
    print(f"\nBackend API Tests:")
    print(f"   Passed: {backend_success}/{backend_total}")
    print(f"   Success Rate: {(backend_success/backend_total)*100:.1f}%")
    
    # Frontend summary
    frontend_success = sum(1 for r in frontend_results.values() if r.get('success', False))
    frontend_total = len([r for r in frontend_results.values() if 'status' in r])
    
    print(f"\nFrontend Tests:")
    print(f"   Passed: {frontend_success}/{frontend_total}")
    print(f"   Success Rate: {(frontend_success/frontend_total)*100:.1f}%" if frontend_total > 0 else "   No tests completed")
    
    # Overall assessment
    total_success = backend_success + frontend_success
    total_tests = backend_total + frontend_total
    
    print(f"\nOverall Assessment:")
    print(f"   Total Tests: {total_tests}")
    print(f"   Total Passed: {total_success}")
    print(f"   Overall Success Rate: {(total_success/total_tests)*100:.1f}%")
    
    # System status
    if total_success == total_tests:
        print("\nSystem Status: ALL TESTS PASSED")
        print("   Backend API: Fully operational")
        print("   Frontend Server: Working correctly")
        print("   Integration: Complete")
        return True
    elif total_success > total_tests * 0.8:
        print("\nSystem Status: MOSTLY OPERATIONAL")
        print("   Some issues detected, but system is functional")
        return False
    else:
        print("\nSystem Status: ISSUES DETECTED")
        print("   Multiple failures require attention")
        return False

def main():
    """Main test function"""
    print("TradingView Bot Integration Test")
    print("=" * 50)
    print(f"Test Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Test backend APIs
    backend_results = test_backend_apis()
    
    # Test frontend server
    frontend_results = test_frontend_server()
    
    # Generate report
    success = generate_test_report(backend_results, frontend_results)
    
    # Portfolio analysis
    if 'balance' in backend_results and backend_results['balance']['success']:
        try:
            balance_response = requests.get("https://web-production-0efa7.up.railway.app/balance", timeout=10)
            if balance_response.status_code == 200:
                balance_data = balance_response.json()
                portfolio_value = calculate_portfolio_value(balance_data)
                
                print(f"\n[PORTFOLIO ANALYSIS]:")
                print(f"   Total Portfolio Value: ${portfolio_value:,.2f}")
                
                for asset in balance_data['balances']:
                    asset_symbol = asset['asset']
                    free_amount = float(asset['free'])
                    locked_amount = float(asset['locked'])
                    total_amount = free_amount + locked_amount
                    
                    if asset_symbol == 'BTC':
                        value = total_amount * 50000
                        print(f"   {asset_symbol}: {total_amount:.8f} BTC ~ ${value:,.2f}")
                    elif asset_symbol == 'ETH':
                        value = total_amount * 3000
                        print(f"   {asset_symbol}: {total_amount:.8f} ETH ~ ${value:,.2f}")
                    elif asset_symbol in ['USDT', 'USD']:
                        print(f"   {asset_symbol}: {total_amount:.2f} = ${total_amount:,.2f}")
        except Exception as e:
            print(f"\n[PORTFOLIO ANALYSIS]: Failed - {e}")
    
    print(f"\n[TEST COMPLETED]: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)